import type { ForgeConfig } from '@electron-forge/shared-types';
import { MakerSquirrel } from '@electron-forge/maker-squirrel';
import { MakerZIP } from '@electron-forge/maker-zip';
import { MakerDeb } from '@electron-forge/maker-deb';
import { MakerRpm } from '@electron-forge/maker-rpm';
import { WebpackPlugin } from '@electron-forge/plugin-webpack';
import { MakerDMG } from '@electron-forge/maker-dmg'
import { mainConfig } from './webpack.main.config';
import { preloadConfig } from './webpack.preload.config';

const platform = process.platform === 'darwin'

const config: ForgeConfig = {
  packagerConfig: {
    name: 'BRYZOS',
    icon: './public/GISS2_0',
    ignore:
      /^\/((?!package\.json|\.webpack).)*$|^\/((?!node_modules).)*[\\/]node_modules[\\/].*$/i,
    osxSign: {},
    osxNotarize: {
      tool: 'notarytool',
      appleId: '<EMAIL>',
      appleIdPassword: 'xzga-sjus-ivof-kxwy',
      teamId: 'DNP7M46CHH',
    }
  },
  rebuildConfig: {},
  makers: [
    new MakerSquirrel({
      loadingGif: './public/GISS2_0.png',
      authors: 'Bryzos LLC',
      description: 'Bryzos Gone In 60 Seconds (GISS) 2.0',
      iconUrl: 'https://widget-ui-wip.s3.amazonaws.com/GISS2_0.ico',
    }),
    new MakerZIP({}),
    new MakerRpm({}),
    new MakerDeb({}),
    new MakerDMG({
      icon: './public/GISS2_0.icns',
      format: 'ULFO',
      background: './public/dmgBackground.png',
      iconSize: 120,
      contents: (arg) => {

        return [
          { x: 460, y: 182, type: 'link', path: '/Applications', name: '' },
          // @ts-ignore
          { x: 185, y: 182, type: 'file', path: arg.appPath, name: '' }
        ]
      },
      additionalDMGOptions: {
        "background-color": '#fff',
        window: {
          size: {
            width: 600,
            height: 414,
          }
        }
      }
    })
  ],
  plugins: [
    new WebpackPlugin({
      mainConfig,
      renderer: {
        config: preloadConfig,
        entryPoints: [
          {
            name: 'main_window',
            preload: {
              js: './src/preload/index.ts',
            },
          },
        ],
      },
    }),
  ],
};

export default config;
