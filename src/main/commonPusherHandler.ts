// @ts-nocheck
import { notificationHandler, showNotification } from './notification';

import config from './config';
import log from "electron-log/main"
import fs from 'fs';
import { ipcMain } from 'electron';
import axios from 'axios';

const { commonAppEventsofPusher } = config

let mainWindow, channelWindow;
const { privateEvents } = commonAppEventsofPusher;

export const commonPusherHandlerInit = (_mainWindow, _channelWindow) => {
    mainWindow = _mainWindow;
    channelWindow = _channelWindow;
    ipcMain.on(channelWindow.uploadLogs, async (event, data) => {
        uploadLogs(data)
    });
}

export const customNotificationHandler = (channel, channelEvent) => {
    channel.bind(channelEvent, (data) => {
        if(data.notification.body.trim().startsWith("####%%%%^^^^|")) return;
        mainWindow?.webContents.send(channelWindow.customNotification, data);
    });
} 

export const uploadLogsToS3Handler = (channel) => {
    channel.bind(privateEvents.userUiUploadLog, (data) => {
        mainWindow?.webContents.send(channelWindow.getSignedUrl, [data.notification]);
        console.log("Recieved USER_UI_UPLOAD_LOG event")
    })
} 

export const uploadLogs = async (data) => {
    try {
        if(data?.pusherData){
            let filePath = log.transports.file.getFile().path
            if(data.pusherData.isOld) filePath = log.transports.file.getFile().path.slice(0,-4)+".old.log"; 

            const fileContent = await fs.promises.readFile(filePath);
            const file = new Blob([fileContent], { type: 'text/plain' }); 
            const response = await axios.put(data?.signedUrl, file, {
                headers: {
                    'Content-Type': 'text/plain', 
                    'Content-Disposition': `attachment; filename="${data?.fileName}"`
                },
            });
            if(response.status == 200){
                if(!data.pusherData.isOld){
                    mainWindow?.webContents.send(channelWindow.markAsReadNotification, JSON.stringify(data.pusherData));
                }
            }else{
                console.error('Error uploading log file:',response.status);
            }
        }
    } catch (error) {
        console.error('Error uploading log file:', error);
    }
};