// @ts-nocheck
import { ipcMain, app, autoUpdater } from 'electron';
import electron from 'electron';
import { isDev } from './helper';
import config from './config';
import { createNewUpdateWindow, browserWindow, channelWindow } from './index';
import axios  from 'axios';

const { updateUrl, branch  } = config

const supportedPlatforms = ['darwin', 'win32'];

let feedURL: string;

export default function updater() {
  // don't attempt to update during development
  // if (isDev) {
  //   const message =
  //     'update-electron-app config looks good; aborting updates since app is in development mode';
  //   console.log(message);
  //   return;
  // }
  ipcMain.on(channelWindow.doUpdate, (event) => {
    isDev?console.log("Cannot update in Development mode"):quitAndInstall();
  });

  ipcMain.on(channelWindow.ready, (event) => {
    log("Update popup ready. Sending release data to it.");
    //event.returnValue = JSON.stringify({currentVersion:app.getVersion() ,releaseNotes:"asdf", releaseName:"release name", releaseDate:"release date", updateURL:"UpdateURL"});
    event.returnValue = {currentVersion:app.getVersion() ,releaseNotes:_releaseNotes, releaseName:_releaseName};
  });

   if(isDev){
     console.log('update-electron-app config looks good; aborting updates since app is in development mode');
    electron.app.isReady() ? initDevUpdater() : electron.app.on('ready', () => initDevUpdater());
    return;
   }

  electron.app.isReady() ? initUpdater() : electron.app.on('ready', () => initUpdater());

};

let _releaseNotes;
let _releaseName;


const quitAndInstall = ()=>{
  autoUpdater.quitAndInstall();
  if(process.platform === 'darwin'){
    setTimeout(()=>{
      app.quit();
    }, 1000)
  }
}

function log(...args) {
  console.log(...args);
  browserWindow?.webContents.send('data-channel', args);
}

/*
  This function is just used for Dev testing. This will blindly assume that we have an update available and will invoke events
*/
function initDevUpdater(){ 
  const server = updateUrl;

  const arch = process.arch === 'arm64' ? '_arm64' : '';
  feedURL = `${server}/update/${process.platform}${arch}/${app.getVersion()}/${branch}`;

  // exit early on unsupported platforms, e.g. `linux`
  if (
    typeof process !== 'undefined' &&
    process.platform &&
    !supportedPlatforms.includes(process.platform)
  ) {
    log(`Electron's autoUpdater does not support the '${process.platform}' platform`);
    return;
  }

  log('feedURL: ', feedURL);
  
  setTimeout(()=>{
    log("Creating update popup:");
    if(process.platform === 'win32'){
      getRelaseNotesAndShowUpdatePopup("NEW_VER")
    }else{
      showUpdatePopup(`<ul>
  <li>New user interface</li>
  <li>Regional & Volume-based pricing</li>
  <li>Ability to upload Bills of Material (PDF)</li>
  <li>Ability to create quotes</li>
  <li>Ability to create draft POs</li>
</ul>`, "NEW_VER");
    }
  }, 8000)
}

function showUpdatePopup(releaseNotes, releaseName){
  // createNewUpdateWindow();
  browserWindow.webContents.send(channelWindow.updateAvailable, {releaseNotes, releaseName , currentVersion:app.getVersion()});
  _releaseNotes = releaseNotes;
  _releaseName = releaseName;
}

async function getRelaseNotesAndShowUpdatePopup(releaseName){
  console.log("fetching release notes");
    let data = await axios.get(feedURL);
    showUpdatePopup(data.data.notes, releaseName);
}
 
function initUpdater() {
 
  const server = updateUrl;

  const arch = process.arch === 'arm64' ? '_arm64' : '';
  feedURL = `${server}/update/${process.platform}${arch}/${app.getVersion()}/${branch}`;

 

  // exit early on unsupported platforms, e.g. `linux`
  if (
    typeof process !== 'undefined' &&
    process.platform &&
    !supportedPlatforms.includes(process.platform)
  ) {
    log(`Electron's autoUpdater does not support the '${process.platform}' platform`);
    return;
  }

  log('feedURL', feedURL);
  autoUpdater.setFeedURL({url: feedURL});

  autoUpdater.on('error', (err) => {
    log('updater error');
    log(err);
  });

  autoUpdater.on('checking-for-update', () => {
    log('checking-for-update');
  });

  autoUpdater.on('update-available', () => {
    log('update-available; downloading...');
  });

  autoUpdater.on('update-not-available', () => {
    log('update-not-available');
  });
 
    autoUpdater.on(
      'update-downloaded',
      (event, releaseNotes, releaseName, releaseDate, updateURL) => {
        log('update-downloaded', [event, releaseNotes, releaseName, releaseDate, updateURL]);
        //  createNewUpdateWindow();
        // _releaseNotes = releaseNotes;
        // _releaseName = releaseName;

        if(process.platform === 'win32'){
          getRelaseNotesAndShowUpdatePopup(releaseName)
        }else{
          showUpdatePopup(releaseNotes, releaseName);
        }


        // const dialogOpts = {
        //   type: 'info',
        //   buttons: ['Restart'],
        //   title: 'Application Update',
        //   message: process.platform === 'win32' ? releaseNotes : releaseName,
        //   detail:
        //     'A new version has been downloaded. Restart the application to apply the updates.',
        // };

        // dialog.showMessageBox(dialogOpts).then(({ response }) => {
        //   if (response === 0) autoUpdater.quitAndInstall();
        // });
      }
    );
 

  // check for updates right away and keep checking later
  setTimeout(() => { 
    autoUpdater.checkForUpdates();
   }, 5000)
  // 15 minutes
  setInterval(() => {
    autoUpdater.checkForUpdates();
  }, 900000);
}
