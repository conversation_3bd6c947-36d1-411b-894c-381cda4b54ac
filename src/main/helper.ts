import { app } from 'electron';

import config from './config';
import { rendererVersion } from '.';
import axios from 'axios';

const { rendererVersionCheckUrl } = config;
export const isDev = !app.isPackaged;

export const urlMasks = [
    {
      "key": "bryzos.com",
      "mask": "TL0hk"
    },
    {
      "key": "vercel.app",
      "mask": "70N1sX"
    },
    {
      "key": "bryzoswidget.com",
      "mask": "x8nZVM7I"
    },
    {
      "key": "bryzosservices.com",
      "mask": "G6Tmy"
    },
    {
      "key": "imgix.net",
      "mask": "uDLGFhKx"
    },
    {
      "key": "imagekit.io",
      "mask": "Ny7PH02R"
    },
    {
      "key": "cloudfront.net",
      "mask": "xXKaBdR"
    },
    {
      "key": "capgo.app",
      "mask": "a8CfxyV7a"
    },
    {
      "key": "amazonaws.com",
      "mask": "U12FEsJH"
    },
    {
      "key": "cassinfo.com",
      "mask": "Fr0mj1v3J"
    },
    {
      "key": "truevault.com",
      "mask": "hsQ6oqGZt"
    },
    {
      "key": "wss://ws-ap2.pusher.com",
      "mask": "h0ttI"
    },
    {
      "key": "|",
      "mask": "__PIPE__"
    }
  ]

export const checkRendererVersion = async (
  channelWindow,
  mainWindow,
  message = 'The app has been updated. Please refresh the app to get the latest features.'
) => {
  if (isDev) return;
  
  try {
    const res = await fetch(`${rendererVersionCheckUrl}`, {
      method: "GET",
      headers: {
        "User-Agent": "bryzos-ua-41cc2924-27dc-4495-bf47-450a5de0950d",
        "Accept": "application/json"
      }
    });

    
    if (!res.ok) {
      throw new Error(`HTTP error! Status: ${res.status}`);
    }
    
    const data = await res.json();

    if (data.data !== rendererVersion) {
      mainWindow?.webContents.send(
        channelWindow.customNotification,
        JSON.parse(`
          {"notification":{"title":"Announcement","body":"${message}","priority":"HIGH","action":"REFRESH"}} 
        `)
      );
    }
  } catch (err) {
    console.log("Error fetching new renderer version", err);
  }
};
