.chatWithVendor {
    height: 466px;
    width: 100%;
    padding: 16px 16px 19px;
    background-color: #191a20;
    display: flex;
    flex-direction: column;
    margin-top: 0;
    min-height: 400px;
    overflow: hidden;
    margin-top: 16px;
    flex:1;

    .chatHeader {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;

        .poNumberMain {
            font-family: Inter;
            font-size: 14px;
            font-weight: 600;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.2;
            letter-spacing: 0.98px;
            text-align: left;
            color: #fff;
            display: flex;
            align-items: center;
            margin-bottom: 3px;
        }

        .vendorName {
            font-family: Inter;
            font-size: 12px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.2;
            letter-spacing: normal;
            text-align: left;
            color: #c3c4ca;

            .vendorRole {
                font-size: 10px;
                font-weight: 300;
                display: block;
                margin-top: 3px;
            }
        }

        .btnCloseContainer {
            display: inline-flex;
            align-items: baseline;
            column-gap: 8px;
        }

    }

    .chatBody {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        flex: 1;
        min-height: 0;


        .chatMessages {
            position: relative;
            width: 100%;
            flex: 1;
            min-height: 0;
            display: flex;
            flex-direction: column;
            border-radius: 10.3px;
            background-color: rgba(217, 217, 217, 0.04);
            padding: 5px;
        }

        .newMessageIndicator {
            position: absolute;
            bottom: 16px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #4a86e8;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            z-index: 10;
            font-size: 14px;
            transition: all 0.2s ease;

            &:hover {
                background-color: #3a76d8;
            }

            .downArrowIcon {
                width: 16px;
                height: 16px;
                fill: white;
            }
        }

        .messageContainer {
            display: flex;
            flex: 1;
            flex-direction: column;
            align-items: flex-start;
            width: 100%;
            overflow-y: auto;
            padding: 12px 8px 16px 8px;


            &::-webkit-scrollbar {
                width: 6px;
                height: 6px;
            }

            &::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb:hover {
                background: rgba(255, 255, 255, 0.5);
            }

            .dropActive {
                border: 2px dashed #3399ff;
                background: rgba(51, 153, 255, 0.1);
                position: relative;
            }

            .dropOverlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(51, 153, 255, 0.2);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.2em;
                color: #3399ff;
                z-index: 2;
                pointer-events: none;
            }

            .message {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                justify-content: flex-end;
                /* This makes the messages stack from the bottom */
                width: 100%;
                height: auto;
                margin-bottom: 12px;

                &.myMessage {
                    align-items: flex-end;
                }

                .chatTimestamp{
                    display: flex;
                    column-gap: 6px;
                    .adminRole{
                           font-size: 10px;
                            font-weight: normal;
                            font-stretch: normal;
                            font-style: normal;
                            line-height: 1;
                            letter-spacing: normal;
                            text-align: center;
                            color: #4a86e8;
                    }
                }

                .messageTimestamp {
                    font-size: 10px;
                    font-weight: normal;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1;
                    letter-spacing: normal;
                    text-align: center;
                    color: #71737f;
                    margin-bottom: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                // Removed .messageBubble styles as they're now in the separate component
            }
        }
    }

    .inputSection {
        flex-shrink: 0;
        margin-top: auto;

        .formattingToolbarContainer {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            margin-top: 6px;

            .formattingToolbar {
                display: flex;
                align-items: center;
                column-gap: 6px;
                position: relative;

                .emojiContainer {
                    display: flex;
                    align-items: center;

                    .emojiPicker {
                        position: absolute;
                        bottom: 29px;
                        left: 0px;
                        width: 281px;
                        max-height: 70px;
                        display: flex;
                        flex-wrap: wrap;
                        gap: 4px;
                        overflow-y: auto;
                        z-index: 10;
                        background-color: rgba(255, 255, 255, 0.5);
                        padding-top: 3px;
                        border-radius: 10.3px;

                        &::-webkit-scrollbar {
                            width: 5px;
                            height: 6px;
                        }


                        // &:before {
                        //     content: '';
                        //     position: absolute;
                        //     bottom: -8px;
                        //     left: 10px;
                        //     width: 0;
                        //     height: 0;
                        //     border-left: 8px solid transparent;
                        //     border-right: 8px solid transparent;
                        //     border-top: 8px solid rgba(0, 0, 0, 0.8);
                        // }

                        .emojiOption {
                            width: 30px;
                            height: 30px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            font-size: 18px;
                            padding: 0;
                            margin: 0;
                            background: transparent;
                            border: none;
                            border-radius: 4px;
                            transition: background-color 0.2s;

                            &:hover {
                                background-color: rgba(255, 255, 255, 0.1);
                            }
                        }
                    }
                }

                .formattingButton {
                    background: transparent;
                    border: none;
                    color: #fff;
                    font-size: 18px;

                    .Icon {
                        width: 20px;
                        height: 20px;
                        display: block;
                    }

                    .IconHover {
                        width: 20px;
                        height: 20px;
                        display: none;
                    }

                    &:hover {
                        .Icon {
                            display: none;
                        }

                        .IconHover {
                            display: block;
                        }
                    }
                }

                .buttonClicked {
                    .Icon {
                        display: none;
                    }

                    .IconHover {
                        display: block;
                    }
                }

                .attachmentIcon {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 20px;
                    height: 20px;
                    cursor: pointer;
                }

                .emojiButton {
                    .Icon {
                        display: none;
                    }

                    .IconHover {
                        display: block;
                    }
                }
            }
        }

        .inputContainer {
            width: 100%;
            height: 70px;
            margin: 12px 0 16px 0px;
            border-radius: 10px;
            box-shadow: inset 4px 4px 10.1px 0 #000;
            // background: url(../../assets/New-images/Share-Pricing/Email.svg) no-repeat;
            background-origin: border-box;
            background-clip: content-box, border-box;
            background-size: cover;
            background-position: bottom;

            .messageInput {
                width: 100%;
                height: 100%;
                padding: 14px 11px 12px 14.5px;
                border-radius: 12px;
                border: 0px;
                background: transparent;
                font-family: Inter;
                font-size: 12px;
                font-weight: normal;
                line-height: 1.55;
                letter-spacing: normal;
                text-align: left;
                color: #fff;
                text-align: left;
                overflow: auto;

                &::-webkit-scrollbar {
                    width: 6px;
                    height: 6px;
                }

                &[data-disabled] {
                    opacity: 0.35;
                }

                &::-webkit-scrollbar-track {
                    background: transparent;
                    border-radius: 3px;
                }

                &::-webkit-scrollbar-thumb {
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 3px;
                }

                &:focus {
                    outline: none;
                }

                // Placeholder styling for contentEditable
                &:empty::before {
                    content: attr(data-placeholder);
                    color: #71737f;
                    pointer-events: none;
                    position: absolute;
                }

            }
        }

        .sendButton {
            width: 68px;
            height: 32px;
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            padding: 0 12px;
            border-radius: 4px;
            background-color: rgba(255, 255, 255, 0.04);
            gap: 8px;
            border-radius: 3px;
            font-family: Syncopate;
            font-size: 12px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: -0.48px;
            text-align: center;
            color: #71737f;
            text-transform: uppercase;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border: none;

            &[disabled] {
                cursor: not-allowed;
                opacity: 0.35;
            }

            &:not([disabled]):hover {
                background-color: rgba(255, 255, 255, 0.04);
            }
        }
    }

    .pendingFilesIndicator {
        display: flex;
        gap: 2px;
    }
}