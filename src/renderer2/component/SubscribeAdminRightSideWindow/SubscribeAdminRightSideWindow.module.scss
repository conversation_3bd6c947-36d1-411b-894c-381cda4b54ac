.title {
    font-family: Syncopate;
    font-size: 20px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.2;
    letter-spacing: 0.8px;
    text-align: center;
    color: #fff;
    margin-bottom: 20px;
}
.paymentDetailsContainer{
    width: 322px;
    height: 450px;
    padding: 20px;
    border-radius: 20px;
    background-origin: border-box;
    position: relative;
    overflow: hidden;
    background: url(/src/renderer2/assets/New-images/Subscription-Includes-BG.svg) no-repeat transparent;
    text-align: center;
}
.adminControlContainer{
    width: 322px;
    height: 520px;
    padding: 20px 4px 19px 20px;
    border-radius: 20px;
    background-origin: border-box;
    position: relative;
    overflow: hidden;
    margin-top: 30px;
    background: url(../../assets/New-images/Create-PO-Order-Ledger.svg) no-repeat transparent;
}
.paymentDetails {
    width: 282px;
    height: 199px;
    padding: 16px 12px;
    border-radius: 13px;
    background-color: rgba(255, 255, 255, 0.04);
    display: flex;
    flex-direction: column;
    gap: 8px;
    .paymentDetailsItem {
      display: flex;
      justify-content: space-between;
      .paymentDetailsItemTitle {
        font-family: Inter;
        font-size: 20px;
        font-weight: 200;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.34;
        letter-spacing: 0.6px;
        text-align: left;
        color: #fff;
      }
      .paymentDetailsItemValue {
        font-family: Inter;
        font-size: 20px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.34;
        letter-spacing: 0.6px;
        text-align: left;
        color: #fff;
      }
    }
}
.paymentDetailsCancelButton {
    font-family: Syncopate;
    font-size: 10px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.61;
    letter-spacing: 0.4px;
    text-align: center;
    color: rgba(255, 255, 255, 0.4);
    margin-top: 12px;
    margin-bottom: 32px;
}
.paymentDetailsEditButton {
    width: 282px;
    height: 68px;
    padding: 19px 41px;
    border-radius: 10px;
    background-color: rgba(255, 255, 255, 0.04);
    span{
        font-family: Syncopate;
        font-size: 16px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.16;
        letter-spacing: normal;
        text-align: center;
        color: rgba(255, 255, 255, 0.4);
    }
}
.adminControl {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
    max-height: 431px;
    overflow-y: scroll;
    overflow-x: hidden;
    &::-webkit-scrollbar {
        width: 5px;
        height: 6px;
    }
    .adminControlButton {
        border-radius: 5px;
        background-color: rgba(255, 255, 255, 0.04);
        height: 40px;
        width: 282px;
        font-family: Syncopate;
        font-size: 16px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: normal;
        text-align: center;
        color: rgba(255, 255, 255, 0.4);
        padding-top: 14px;
        padding-bottom: 14px;
        &:hover {
            color: #fff;
        }
        &:focus {
            color: #fff;
        }
        &:last-child {
            margin-bottom: 20px;
        }
    }
}