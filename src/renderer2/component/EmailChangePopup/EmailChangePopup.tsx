import React, { useEffect, useState } from 'react'
import styles from '../../pages/buyer/newSettings/tabs/TabContent.module.scss';
import { Dialog } from '@mui/material';
import { ReactComponent as CloseIcon } from '../../assets/New-images/close-icon.svg';
import InputWrapper from '../InputWrapper';
import CustomTextField from '../CustomTextField';
import { commomKeys, decryptData, emojiRemoverRegex, getChannelWindow, useGlobalStore } from '@bryzos/giss-ui-library';
import { useForm } from 'react-hook-form';
import useDialogStore from '../DialogPopup/DialogStore';
import { Auth } from 'aws-amplify';

const EmailChangePopup = ({showOtpDialog, setShowOtpDialog,email, setIsFadeLoaderOpen,resetData, handleSave, effectiveContainerRef}: any) => {
  const {
    register: otpRegister,
    handleSubmit: otpHandleSubmit,
    formState: { errors: otpErrors },
    watch: otpWatch,
    setError: setOtpError,
    reset: otpReset,
  } = useForm({
    defaultValues: {
      otp: '',
    },
  });
  const [otpCustomError,setOtpCustomError] = useState<any>("");
  const {showCommonDialog, resetDialogStore}: any = useDialogStore();
  const { decryptionEntity  , setForceLogout, showLoader}: any = useGlobalStore();

  const handleOtpSubmit = async (data: any) => {
    try{
      // TODO: Verify OTP with server
      // If successful, update the email
      // If failed, show error
      setIsFadeLoaderOpen(true);
      await Auth.verifyCurrentUserAttributeSubmit('email', data.otp); 
      const channelWindow = getChannelWindow();
      const cred = window.electron.sendSync({ channel: channelWindow.getLoginCredential });
      let password = null;
      if (cred) {
          const data = JSON.parse(await decryptData(cred, decryptionEntity.decryption_key, true));
          password = data.password;
      }
      document.cookie.split(";").forEach((c) => {
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, `=;expires=${new Date().toUTCString()};path=/`);
      });
      await Auth.signIn(email, password);
      // await handleSubmit((data) => handleSaveUser(data, true))();
      await handleSave()
      setShowOtpDialog(false);
      showCommonDialog(null,'Email changed successfully', commomKeys.actionStatus.success, handleOtpSuccess, [{name: commomKeys.successBtnTitle, action: handleOtpSuccess}])
      setTimeout(() => {
        handleOtpSuccess();
      }, 5000);
    }catch(error){
      setOtpCustomError ('Wrong OTP. Please try again.');
      console.error('error', error);
    }finally{
      otpReset();
      setIsFadeLoaderOpen(false);
    }
  };

  const handleOtpCancel = () => {
    setShowOtpDialog(false);
    otpReset();
    resetData();
    setIsFadeLoaderOpen(false);
  };

  const handleOtpResend = async () => {
    const newEmail = email;
    const user = await Auth.currentAuthenticatedUser();
    await Auth.updateUserAttributes(user, { email: newEmail });
  };

  const handleOtpSuccess = () => {
    setForceLogout(true);
    resetDialogStore()
  }

  return (
    <div className={styles.emailChangePopup}>
       {/* OTP Verification Dialog */}
       <Dialog
        open={showOtpDialog}
        onClose={handleOtpCancel}
        transitionDuration={100}
        container={effectiveContainerRef.current}
        disableScrollLock={true}
         style={{
          position: 'absolute',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backdropFilter: 'blur(7px)',
          WebkitBackdropFilter: 'blur(7px)',
          backgroundColor: 'rgba(0, 0, 0, 0.23)',
          border: '1px solid transparent',
          borderRadius: '0px 0px 10px 10px',
          opacity: showLoader ? 0 : 1
        }}
        PaperProps={{
          style: {
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            margin: 0,
          },
        }}
        hideBackdrop
        classes={{
          root: styles.changePassDialog,
          paper: styles.dialogContent,
        }}
      >
        <button
          className={styles.closeIcon}
          onClick={handleOtpCancel}
        >
          <CloseIcon />
        </button>
        
        <div className={styles.changePasswordWrapper}>
          <h2 className={styles.changePasswordTitle}>change your email</h2>
          
          <div className={styles.changePasswordContent}>
            <div className={styles.verificationEmailMain}>
             <div className={styles.instructionText}>Enter Your New Email</div>
             <div className={styles.emailDisplay}>{email}</div>
            </div>
          
             
            <form onSubmit={otpHandleSubmit(handleOtpSubmit)}>
              <div className={styles.inputGroup}>
                <div className={styles.instructionText}>ONE-TIME PASSWORD (OTP)</div>
                <div className={styles.inputMainOtp}>
                  <InputWrapper>
                  <CustomTextField 
                    register={otpRegister("otp")}
                    className={styles.otpInput}
                    onChange={(e: any) => {
                      setOtpCustomError('');
                      e.target.value = e.target.value.replace(emojiRemoverRegex, '');
                      otpRegister("otp").onChange(e);
                    }}
                    // placeholder='Enter 6-digit code' 
                    maxLength={6} 
                    mode="wholeNumber" 
                    errorInput={otpCustomError}
                  />
                </InputWrapper>

                 {otpCustomError? <p className={styles.errorText}>{otpCustomError}</p> : null}
                </div>
                
              </div>
              
              <div className={styles.buttonGroup}>
                <button
                  type="button"
                  onClick={handleOtpResend}
                  className={styles.resendOtpBtn}
                >
                  Resend OTP
                </button>
                <button
                  type="submit"
                  className={styles.verifyButton}
                  disabled={!(otpWatch('otp')?.length)}
                >
                  Continue
                </button>
              </div>
            </form>
          </div>
        </div>
      </Dialog>
    </div>
  )
}

export default EmailChangePopup
