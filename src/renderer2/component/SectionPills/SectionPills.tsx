// SectionPills.tsx
// React + TypeScript + SCSS Modules (no UI libs)
// A horizontal, scrollable pill-tab selector matching your mock.

import React, { useEffect, useMemo, useRef, useState } from "react";
import clsx from "clsx";
import styles from "./SectionPills.module.scss";

export type SectionPillItem = {
  id: string;
  label: string;
  disabled?: boolean;
};

export interface SectionPillsProps {
  items: SectionPillItem[];
  activeId?: string;                  // controlled: currently selected id
  defaultActiveId?: string;           // uncontrolled initial selection
  onChange?: (id: string, item: SectionPillItem, index: number) => void;
  className?: string;
  scrollBehavior?: "smooth" | "auto"; // how the active pill scrolls into view
  size?: "sm" | "md";                 // spacing/height preset
}

export default function SectionPills({
  items,
  activeId,
  defaultActiveId,
  onChange,
  className,
  scrollBehavior = "smooth",
  size = "md",
}: SectionPillsProps) {
  const isControlled = activeId !== undefined;
  const [internalId, setInternalId] = useState<string | undefined>(
    defaultActiveId ?? items[0]?.id
  );
  const currentId = isControlled ? activeId : internalId;

  const listRef = useRef<HTMLDivElement>(null);
  const btnRefs = useRef<Map<string, HTMLButtonElement>>(new Map());

  const indexById = useMemo(() => {
    const map = new Map<string, number>();
    items.forEach((it, i) => map.set(it.id, i));
    return map;
  }, [items]);

  useEffect(() => {
    const btn = currentId ? btnRefs.current.get(currentId) : null;
    if (btn && listRef.current) {
      btn.scrollIntoView({ inline: "nearest", block: "nearest", behavior: scrollBehavior });
    }
  }, [currentId, scrollBehavior]);

  const select = (id: string) => {
    if (!isControlled) setInternalId(id);
    const idx = indexById.get(id) ?? -1;
    const item = items[idx];
    if (item) onChange?.(id, item, idx);
  };

  const moveFocus = (delta: number) => {
    const ids = items.map((i) => i.id);
    const curIdx = currentId ? indexById.get(currentId) ?? 0 : 0;
    let next = curIdx;
    do {
      next = (next + delta + ids.length) % ids.length;
    } while (items[next]?.disabled && next !== curIdx);
    const nextId = ids[next];
    btnRefs.current.get(nextId)?.focus();
  };

  return (
    <div
      ref={listRef}
      className={clsx(styles.wrap, className, styles[size])}
      role="tablist"
      aria-label="Sections"
    >
      {items.map((item, i) => {
        const selected = item.id === currentId;
        return (
          <button
            key={item.id}
            ref={(el) => {
              if (el) btnRefs.current.set(item.id, el);
              else btnRefs.current.delete(item.id);
            }}
            role="tab"
            aria-selected={selected}
            tabIndex={selected ? 0 : -1}
            disabled={item.disabled}
            className={clsx(styles.pill, selected && styles.active, item.disabled && styles.disabled)}
            onClick={() => !item.disabled && select(item.id)}
            onKeyDown={(e) => {
              if (e.key === "ArrowRight") { e.preventDefault(); moveFocus(1); }
              if (e.key === "ArrowLeft")  { e.preventDefault(); moveFocus(-1); }
              if ((e.key === "Enter" || e.key === " ") && !item.disabled) {
                e.preventDefault(); select(item.id);
              }
            }}
          >
            {item.label}
          </button>
        );
      })}
    </div>
  );
}
