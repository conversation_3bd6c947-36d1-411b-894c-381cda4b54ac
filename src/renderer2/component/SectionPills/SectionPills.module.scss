/* SectionPills.module.scss */
/* Dark theme pills with rounded corners; one active light pill like your screenshot. */

.wrap {
  display: flex;
  gap: 10px;
  width: calc(100% - 32px);
  overflow-x: scroll;
  overflow-y: hidden;
  padding-bottom: 16px;
  height: 70px;

  &::-webkit-scrollbar {
    height: 4px;
  }
}

.pill {
  border-radius: 500px;
  background-color: rgba(255, 255, 255, 0.04);
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: center;
  color: #71737f;
  padding: 8px 12px;
  white-space: nowrap;
  height: 36px;

  &:hover {
    color: #fff;
  }
}

.active {
  background-color: #c3c4ca;
  color: #0f0f14;
  &:hover {
    background-color: #c3c4ca;
    color: #0f0f14;
  }
}

.disabled {
  opacity: 0.45;
  cursor: not-allowed;
}
