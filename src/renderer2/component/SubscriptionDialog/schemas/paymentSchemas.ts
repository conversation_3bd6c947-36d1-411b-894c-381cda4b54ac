import { PAYMENT_METHODS } from 'src/renderer2/common';
import * as yup from 'yup';

// Base schema for common fields
const baseSchema = yup.object({
  numberOfLicenses: yup
    .number()
    .min(1, 'Minimum 1 license required')
    .max(100, 'Maximum 100 licenses allowed'),
  paymentMethod: yup
    .string()
    .oneOf([PAYMENT_METHODS.CARD, PAYMENT_METHODS.ACH], 'Invalid payment method'),
});

// Credit Card schema
export const creditCardSchema = baseSchema.concat(
  yup.object({
    cardholderFirstName: yup
      .string(),
    cardholderLastName: yup
      .string(),
    billingZipCode: yup
      .string(),
  })
);

// Bank Transfer schema
export const bankTransferSchema = baseSchema.concat(
  yup.object({
    // ACH fields
    accountName: yup.string().required('Account name is required'),
    accountType: yup.string().required('Account type is required'),
    routingNumber: yup.string().required('Routing number is required')
      .matches(/^\d{9}$/, 'Routing number must be exactly 9 digits')
      .test('valid-routing-number', 'Invalid routing number format', (value) => {
        if (!value) return false;

        // Basic checksum validation for US routing numbers
        const digits = value.split('').map(Number);
        if (digits.length !== 9) return false;

        const sum =
          3 * (digits[0] + digits[3] + digits[6]) +
          7 * (digits[1] + digits[4] + digits[7]) +
          (digits[2] + digits[5] + digits[8]);

        return sum % 10 === 0;
      }),
    bankName: yup.string().required('Bank name is required'),
    accountNumber: yup.string().required('Account number is required')
      .matches(/^\d+$/, 'Account number must contain only digits')
      .min(8, 'Account number must be at least 8 digits')
      .max(17, 'Account number must not exceed 17 digits'),
    reEnterAccountNumber: yup.string().required('Please confirm account number')
      .oneOf([yup.ref('accountNumber')], 'Account numbers must match'),
  })
);

// Dynamic schema based on payment method
export const getPaymentSchema = (paymentMethod: string) => {
  switch (paymentMethod) {
    case PAYMENT_METHODS.CARD:
      return creditCardSchema;
    case PAYMENT_METHODS.ACH:
      return bankTransferSchema;
    default:
      return baseSchema;
  }
};

export type CreditCardFormData = yup.InferType<typeof creditCardSchema>;
export type BankTransferFormData = yup.InferType<typeof bankTransferSchema>;
export type PaymentFormData = CreditCardFormData | BankTransferFormData; 