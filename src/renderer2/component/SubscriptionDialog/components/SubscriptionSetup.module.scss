.subscriptionSetup {
  color: white;
  font-family: 'Inter', sans-serif;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 0 20px;

    .stepTitle {
      font-size: 24px;
      font-weight: 600;
      color: white;
    }

    .licenseCount {
      font-size: 16px;
      color: #007bff;
      font-weight: 500;
    }
  }

  .content {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
    margin-top: 20px;

    .panel {
     padding: 16px;
      border-radius: 13px;
      background-color: rgba(255, 255, 255, 0.04);
      min-height: 300px;
      display: flex;
      flex-direction: column;

      .panelTitle {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 20px;
        color: #e0e0e0;
      }

      .quantityInput {
        margin-bottom: 20px;

        .quantityField {
          width: 100%;
          height: 60px;
          background: #404040;
          border: none;
          border-radius: 8px;
          font-size: 24px;
          font-weight: 600;
          text-align: center;
          color: white;
          padding: 0 16px;

          &:focus {
            outline: none;
            box-shadow: 0 0 0 2px #007bff;
          }
        }
      }

    
      .priceInfo {
        font-size: 14px;
        color: #b0b0b0;
        margin-top: auto;
      }

      .paymentMethod {
        margin-bottom: 20px;

        .paymentSelect {
          width: 100%;
          height: 50px;
          background: #404040;
          border: none;
          border-radius: 8px;
          font-size: 16px;
          color: white;
          padding: 0 16px;
          cursor: pointer;

          &:focus {
            outline: none;
            box-shadow: 0 0 0 2px #007bff;
          }

          option {
            background: #404040;
            color: white;
          }
        }
      }

      .cardElement {
        margin-top: 20px;
        padding: 16px;
        background: #404040;
        border-radius: 8px;
        border: 1px solid #555;
      }

      .orderSummary {
        margin-bottom: 20px;

        .summaryRow {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid #404040;

          &:last-child {
            border-bottom: none;
          }

          .monthlyTotal {
            font-weight: 700;
            color: white;
          }
        }
      }

      .disclaimer {
        font-size: 12px;
        color: #b0b0b0;
        margin-bottom: 20px;
        line-height: 1.4;

        .link {
          color: #ff6b35;
          cursor: pointer;
          text-decoration: underline;

          &:hover {
            color: #ff8c5a;
          }
        }
      }

      .purchaseButton {
        width: 100%;
        height: 50px;
        background: #404040;
        border: none;
        border-radius: 8px;
        color: white;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover:not(:disabled) {
          background: #505050;
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 1024px) {
  .subscriptionSetup {
    .content {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }
}

@media (max-width: 768px) {
  .subscriptionSetup {
    padding: 16px;

    .header {
      flex-direction: column;
      gap: 12px;
      text-align: center;

      .stepTitle {
        font-size: 20px;
      }

      .licenseCount {
        font-size: 14px;
      }
    }
  }
} 
.editPaymentMain {
  display: flex;
} 

.paymentForm {
  flex-grow: 0;
  border-radius: 13px;
  background-color: rgba(255, 255, 255, 0.04);
  width: 498px;
  min-height: 212px;
  margin: 0 auto;
  padding: 25px;
  margin-bottom: 26px;

  &.isEditLicenseModule {
    background-color: transparent;
    width: 100%;
    padding: 0px;
    margin-bottom: 0px;
  }

  label {
    display: none;
  }

}

.selectDropdown.selectDropdown {
  width: 100%;

  :global(.MuiSelect-select) {
    width: 100%;
    height: 36px;
    flex-grow: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 0px 12px;
    border-radius: 11px;
    border: solid 1px rgba(255, 255, 255, 0.16);
    background-color: #222329;
    font-family: Syncopate;
    font-size: 14px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: -0.56px;
    text-align: left;
    color: #fff;
    text-transform: uppercase;
  }

  :global(.Mui-disabled) {
    opacity: 0.3;
    cursor: not-allowed;
  }

  :global(.MuiSelect-icon) {
    width: 16px;
    height: 16px;
    top: unset;
    right:12px;
      path {
      fill: #71737f;
      stroke-opacity: unset;
    }

  }
  :global(fieldset) {
    border-color: transparent !important;
  }
}

.Dropdownpaper.Dropdownpaper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  gap: 8px;
  padding: 4px;
  border-radius: 8px;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  background-color: #9c9da5;
  padding: 0px;
  margin-top: 1px;

  .muiMenuList {
    padding: 4px;

    li {
      padding: 6px 16px;
      border-radius: 6px;
      font-family: Inter;
      font-size: 14px;
      font-weight: 500;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: center;
      color: #191a20;
      background-color: transparent;

      &:hover {
        background-color: #e0e0e0;
        color: #191a20;
        font-weight: bold;
      }
    }
  }


}


.editLicenseBtnMain{
    width: 100%;
    min-height: 297px;
    padding: 0px;
    background-color: transparent;
    border: none;
}

