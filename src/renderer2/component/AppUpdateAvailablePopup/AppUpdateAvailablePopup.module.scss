.modal {
  border-radius: 12px;
  padding: 55px;
  width: 100%;
  position: relative;
  margin: 0 auto;
  z-index: 9999999;
  max-width: 560px;
  border-radius: 20px;
  background: url(../../assets/New-images/New-Image-latest/new-update-popup-background.svg);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.closeButton {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  color: #888;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
  }
}

.title {
  font-family: Syncopate;
  font-size: 28px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: -1.12px;
  text-align: left;
  color: #fff;
  text-transform: uppercase;
}

.versionInfo {
  font-family: Inter;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: normal;
  text-align: left;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 16px;
  margin-bottom: 24px;
}

.updateContents {
  margin-bottom: 32px;
  font-family: Inter;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: -0.16px;
  text-align: left;
  color: #fff;
  border-radius: 16px;
  background-color: rgba(255, 255, 255, 0.04);
  padding: 16px;
  height: 272px;

  // Style for release notes content
  :global(.releaseNotesContent) {
    overflow-y: auto;
    height: 95%;
    &::-webkit-scrollbar {
      width: 5px;
      height: 6px;
    }

    ul {
      list-style: none;
      padding: 16px 0 0 16px;
      margin: 0;
    }

    li {
      list-style: none;
      padding: 6px 0;
      margin: 0;
      display: flex;
      align-items: center;
      
      // Add arrow icon using CSS
      &::before {
        content: "";
        background-image: url('../../assets/New-images/icon-right-arrow.svg');
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        width: 20px;
        height: 20px;
        margin-right: 8px;
        display: inline-block;
        filter: brightness(0) invert(1); // Makes the SVG white
      }
    }
  }
}

.featureList {
  list-style: none;
  padding: 0;
  margin: 0;

  li {
    display: flex;
    align-items: center;
    color: #ffffff;
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 12px;
    padding: 8px 0;
  }
}

.featureIcon {
  color: #4a9eff;
  font-size: 18px;
  margin-right: 12px;
  font-weight: bold;
  display: inline-block;
  width: 20px;
  text-align: center;
}

.actionButtons {
  display: flex;
  gap: 24px;
  justify-content: center;
}

.keepCurrentButton,
.updateAppButton {
  height: 50px;
  border: none;
  font-family: Syncopate;
  font-size: 18px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: -0.72px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  width: 50%;
  border-radius: 10px;
}

.keepCurrentButton {
  background-color: #222329;
  color: rgba(255, 255, 255, 0.4);
  &:hover {
    color: rgba(255, 255, 255);
  }
}

.updateAppButton {
  background-color: #ffffff;
  color: #0f0f14;
}
