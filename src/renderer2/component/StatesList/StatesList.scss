.states-list {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  box-sizing: border-box;
  margin: 0 auto;
  max-height: 68px;
  height: 68px;
  border-radius: 8px;
  background-color: #3e3f47;
  padding: 3px 6px 8px 6px;
  align-items: center;
  justify-content: space-around;

  .state-button {
    font-family: Syncopate;
    font-size: 10px;
    line-height: normal;
    font-weight: normal;
    letter-spacing: 0.4px;
    text-align: left;
    color: #fff;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    flex: 0 1 auto;
    max-width: calc(100% - 16px);
    padding: 5px 1.5px 3px 2px;
    border-radius: 2px;
    width: 26px;
    margin-right: 1px;

    &:last-child {
      margin-right: 0px;
    }

    &.active {
      background-color: transparent;
      color: #fff;
    }

    &.unactive {
      opacity: 0.2;
      color: #fff;
    }

    &:hover {
      background-color: #ffe352;
      color: #0f0f14;
      font-weight: bold;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    &:active {
      transform: translateY(0);
      box-shadow: none;
    }

    &:focus {
      outline: none;
      border-color: #ffe352;
      box-shadow: 0 0 0 3px rgba(77, 171, 247, 0.2);
    }

  }

    &.stateMain1{
      .state-button {
       &:hover {
            background-color: #32ccff;
            color: #fff;
        }
        &:focus {
          background-color: #32ccff;
            color: #fff;
        }
      }
   
    }
   

  .state-button.hover {
    color: #0f0f14;
    background-color: #ffe352;

    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-weight: bold;
  }
}