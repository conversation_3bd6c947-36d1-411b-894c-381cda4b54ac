import React from 'react';
import './StatesList.scss';
import { routes } from 'src/renderer2/common';
import clsx from 'clsx';

const StatesList = ({ states, onStateClick, inputValue, selectedIndex }) => {
  
  const handleClick = (state) => {
    onStateClick(state);
  };
  
  return (
    <div className={clsx("states-list", location.pathname === routes.quotePage && 'stateMain1')}>
      {states.map((state, index) => (
        <button
          key={state.id}
          className={`state-button ${
            (selectedIndex !== -1 && index === selectedIndex) ? 'hover' : ''
          } ${
            (inputValue && state.code.toLowerCase().startsWith(inputValue.toLowerCase())) ? 'active' : inputValue ? 'unactive' : ''
          }`}
          onClick={() => handleClick(state)}
        >
          {state.code}
        </button>
      ))}
    </div>
  );
};

export default StatesList; 