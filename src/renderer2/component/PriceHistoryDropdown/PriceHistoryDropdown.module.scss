.pricePerUnitContainer {
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: 15px;

  &:hover {
    .priceHistoryDropdown {
      display: block;
    }
  }

  .priceHistoryIndicator {
    font-size: 8px;
    color: rgba(255, 255, 255, 0.6);
    margin-left: 2px;
  }

  .priceChangeArrow {
    font-size: 10px;
    font-weight: 600;
    margin-right: 4px;
    display: inline-block;
    cursor: pointer;
  }
}

.priceChangeIndicator {
  display: flex;
  align-items: center;
  gap: 4px;
      margin-top: 8px;
    padding-bottom: 8px;

  &.priceChangeIndicatorShow {
    padding: 0px 3px 0px 4px;
     background-color: #393e47;
    border-radius: 8px 8px 0px 0px;
    height: 42px;
    margin-top: 8px;
    padding-bottom: 8px;
    .selectUom{
      display:block
     }
  }

}

.selectUomValue{
   font-family: Inter;
    font-size: 14px;
    line-height: normal;
    letter-spacing: normal;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
    height: 45px;
  
    border: 0;
    text-transform: uppercase;
}

.selectUom1 {
  .uomDrodown {
    font-family: Inter;
    font-size: 14px;
    line-height: normal;
    letter-spacing: normal;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
    height: 45px;
    padding-left: 4px;
    border: 0;
    text-transform: uppercase;

    &:focus-visible {
      outline: none;
    }

    div:nth-child(1) {
      padding: 0px 23px 0px 0px;
    }

    .MuiSelect-select {
      padding: 20px 6px 6px 6px;
    }

    svg {
      transform: unset;
      color: rgba(255, 255, 255, 0.6);
      right: 0;
    }

    fieldset {
      border: 0;
    }
  }
}

.selectUom {
  .uomDrodown {
    font-family: Inter;
    font-size: 14px;
    line-height: normal;
    letter-spacing: normal;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
    height: 45px;
    padding-left: 18px;
    border: 0;
    text-transform: uppercase;

    &:focus-visible {
      outline: none;
    }

    div:nth-child(1) {
      padding: 0px 32px 0px 0px;
    }

    .selectDropdown {
      padding: 0px;
    }

    .MuiSelect-select {
      padding: 20px 6px 6px 6px;
    }

    svg {
      transform: unset;
      color: rgba(255, 255, 255, 0.6);
      right: 10px;
    }

    fieldset {
      border: 0;
    }
  }
}

.selectUomPaper.selectUomPaper {
  padding: 4px;
  border-radius: 6px;
  -webkit-backdrop-filter: blur(22.4px);
  backdrop-filter: blur(22.4px);
  background-color: rgba(128, 130, 140, 0.28);
  box-shadow: none;

  ul {
    overflow: auto;
    max-height: 260px;
    padding: 0;

    &::-webkit-scrollbar {
      width: 6px;
    }

    li {
      text-transform: uppercase;
      margin-bottom: 1px;
      padding: 6px;
      border-radius: 5px;
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: center;
      color: rgba(255, 255, 255, 0.6);

      &[aria-selected="true"] {
        background-color: rgba(255, 255, 255, 0.2);
        font-weight: bold;
        color: #fff;
      }

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
        font-weight: bold;
        color: #fff;
      }

    }

    .Mui-selected.Mui-selected {
      background-color: rgba(255, 255, 255, 0.2);
      font-weight: bold;
      color: #fff;
    }
  }
}

.priceHistoryDropdown.priceHistoryDropdown {
  min-width: 337px;
  // height: 54px;
  padding: 7px 8px 10px 11px;
  border-radius:8px;
   background-color: #393e47;

  margin-top: -1px;
  box-shadow: none;

  .priceHistoryRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0px 0;

    &:last-child {
      border-bottom: none;
    }

    .priceHistoryLabel {
     font-family: Inter;
      font-size: 12px;
      font-weight: 300;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: 0.84px;
      text-align: left;
      color: #fff;
      min-width: 70px;
      text-align: left;
    }

    .priceHistoryValues {
      margin-top: 12px;
      display: flex;
      align-items: center;
      gap: 12px;
      font-family: Inter;
      font-size: 12px;
      font-weight: 300;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: 0.84px;
      text-align: center;
      color: #fff;
      .priceHistoryQty,
      .priceHistoryUnit,
      .priceHistoryTotal {
        color: #aaa;
        font-weight: 400;
      }

      .priceHistoryPrice {
        font-family: Inter;
        font-size: 12px;
        font-weight: 300;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: 0.84px;
        text-align: center;
        color: #fff;
        display: flex;
        align-items: center;
        gap: 4px;

        &.currentPrice {
          color: #ff8c4c;
          font-weight: 600;
        }

        .currentPriceArrow {
          font-size: 12px;
          color: #ff8c4c;
          font-weight: 600;
        }
      }
    }
  }
}