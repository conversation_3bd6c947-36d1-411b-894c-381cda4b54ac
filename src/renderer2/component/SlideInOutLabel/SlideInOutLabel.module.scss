  
.feedbackPosition{
    position: absolute;
      bottom: 6.034375rem;
      right: 1.22rem;
      width: 7.0625rem;
      height: 1.0375rem;
      border-radius: 6.25rem;
      background-color: rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
  
    .feedbackSaved{
      padding-left: 8px;
      font-family: Inter;
      font-size: 10px;
      font-weight: 300;
      line-height: 1;
      letter-spacing: 0.7px;
      text-align: left;
      color: #10bbff;
  
    }
  }
  
  
  .overlayForAnimation{
    position: absolute;
    inset: -3px -8px;
    border-radius: 10px 0px 0px 10px;
    background-color: #1b1b21;
  }
  
  .animationShowEaseIn{
    animation: increaseWidth 0.85s linear forwards;
    @keyframes increaseWidth {
      0% {
        right: -8px;
        opacity: 1;
      }
      100% {
        right: 230px;
        opacity: 0;
      }
    }
  }
  
  .animationHideEaseOut2 {
    animation: decreaseWidth 1s linear forwards;
  
    @keyframes decreaseWidth {
      0% {
        right: 230px;
        opacity: 0;
      }
      100% {
        right: -8px;
        opacity: 1;
      }
    }
  }
  
  .animationHideEaseOut{
    animation: reverseGrow 1s linear forwards;
    @keyframes reverseGrow {
      0%{
        right: 140px;
      }
      30% {
        right: 140px;
      }
      100% {
        right: -8px;
      }
    }
  }
  
  .editArrowStyle {
    display: block;
    margin-top: 80px;
  }
  
  .feedbackContent.feedbackContent {
    width: auto;
    height: 16.6px;
    padding: 2px 24px 2px 5.5px;
    border-radius: 100px;
    background-color: rgba(255, 255, 255, 0.1);
    font-family: Inter;
    font-size: 10px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    letter-spacing: 0.7px;
    text-align: right;
    color: #fff;
    position: relative;
    left: 28.4px;
    top: -2.5px;
    margin: 0px;
  
    &.focusSingleProduct{
      background-color:  rgba(25, 25, 29, 0.7);
      .overlayForAnimation{
       background-color: rgba(255, 255, 255, 0.5);
      }
    }
  
    .feedbackInnerContent {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 100%;
  
      .quantityChange {
        // cursor: pointer;
        font-family: Inter;
        font-size: 10px;
        font-weight: 300;
        line-height: 1;
        letter-spacing: 0.7px;
        text-align: right;
        color: #10bbff;
      }
    }
  }
  
  
  .blurEffect{
    .overlayForAnimation{
      background-color:  #302e34
    }
  }
  
  .overlayOnBlur{
    .overlayForAnimation{
      background-color:  rgb(233, 236, 240) !important
    }
  }
  .editFeedback{
    position: relative;
    .feedbackDetail{
      padding-right: 8px;
    }
  }
  
  .feedBackArrow {
    position: absolute;
    bottom: 6.1875rem;
    right: 1.325rem;
    display: flex;
  }