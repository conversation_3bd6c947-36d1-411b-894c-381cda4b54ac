import React, { useState } from 'react'
import styles from './SlideInOutLabel.module.scss'
import clsx from 'clsx'
import { Tooltip } from '@mui/material'
import { ReactComponent as FeedbackArrow } from '../../assets/New-images/feedback-arrow.svg';
interface SlideInOutLabelProps {
    showSlideOutLabel: boolean;
    slideOutContent: React.ReactNode;
    showSlideInLabel: boolean;
    slideInContent: React.ReactNode;
    setShowSlideInLabel: (showSlideInLabel: boolean) => void;
    parentClassName?: string;
    classes?: {
        root?: string;
        feedbackPosition?: string;
        overlayForAnimation?: string;
        feedbackSaved?: string;
        editArrowStyle?: string;
        tooltip?: string;
        editFeedback?: string;
        feedbackInnerContent?: string;
        feedBackArrow?: string;
    };
}


const SlideInOutLabel = (slideInOutLabelProps: SlideInOutLabelProps) => {
    const { showSlideOutLabel, slideOutContent, showSlideInLabel, slideInContent, setShowSlideInLabel, classes } = slideInOutLabelProps;
    const [isBlurred, setIsBlurred] = useState(false);
    const [openTooltip, setOpenTooltip] = useState(false)
    
const handleTooltipOpen = () => {
    setOpenTooltip(true);
    setIsBlurred(false);
  };
  
const handleTooltipClose = (_timeout = 1000) => {
    setIsBlurred(true);
    if(_timeout !== 0){
        setTimeout(() => {
            setOpenTooltip(false);
        }, _timeout);
    }
    else{
        setOpenTooltip(false);
    }
  };
    return (

        <div className={classes?.root}>
            {showSlideInLabel && <div className={clsx(styles.feedbackPosition, classes?.feedbackPosition)}>
                <div className={clsx(styles.overlayForAnimation, styles.animationHideEaseOut, classes?.overlayForAnimation)}></div>
                <div className={clsx(styles.feedbackSaved, classes?.feedbackSaved)}>{slideInContent}</div>
            </div>}
            {showSlideOutLabel &&
                <>
                    <div className={clsx(styles.editArrowStyle, classes?.editArrowStyle)} onClick={(e) => e.stopPropagation()}>
                        <Tooltip
                            placement="left-start"
                            open={openTooltip}
                            onOpen={handleTooltipOpen}  // Trigger to show the tooltip
                            onClose={() => handleTooltipClose(1000)}  // Trigger to hide the tooltip
                            classes={{
                                tooltip: clsx(
                                    // focusSingleProduct[product.id] && styles.focusSingleProduct,
                                    styles.feedbackContent,
                                    classes?.tooltip,
                                    (isBlurred) && styles.blurEffect,
                                    // (isBlurred) && styles.overlayOnBlur
                                ),
                            }}
                            title={
                                <div className={clsx(styles.editFeedback, classes?.editFeedback)}>
                                    <div className={clsx(styles.overlayForAnimation, styles.animationShowEaseIn, isBlurred && styles.animationHideEaseOut2, classes?.overlayForAnimation)}>

                                    </div>
                                    <div className={clsx(styles.feedbackInnerContent, classes?.feedbackInnerContent)}>
                                        {slideOutContent}
                                    </div>
                                </div>
                            }
                        >
                            <span
                                className={clsx(styles.feedBackArrow, classes?.feedBackArrow)}
                                onMouseEnter={() => setShowSlideInLabel(false)}
                            >
                                <FeedbackArrow />
                            </span>
                        </Tooltip>
                    </div>
                </>
            }
        </div>
    )
}

export default SlideInOutLabel