import React, { useState, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';

interface ScrollerProps {
  containerHeight: number; // Height of the main container
  contentHeight: number; // Total height of the scrollable content
  trackWidth?: number;
  thumbWidth?: number;
  thumbColor?: string;
  trackColor?: string;
  onScrollChange?: (scrollTop: number) => void; // Called during drag to update main content
  className?: string;
  style?: React.CSSProperties;
  rightOffset?: number; // Distance from right edge
  topOffset?: number; // Distance from top edge
  bottomOffset?: number; // Distance from bottom edge
  fixedThumbHeight?: number; // Fixed height for the thumb (default: 40px)
}

export interface ScrollerRef {
  updateScrollPosition: (scrollTop: number) => void;
  getScrollPosition: () => number;
  isAtBottom: (threshold?: number) => boolean;
}

const Scroller = forwardRef<ScrollerRef, ScrollerProps>(({
  containerHeight,
  contentHeight,
  trackWidth = 10,
  thumbWidth = 5,
  thumbColor = '#0f0f14',
  trackColor = 'transparent',
  onScrollChange,
  className = '',
  style = {},
  rightOffset = 10,
  topOffset = 0,
  bottomOffset = 0,
  fixedThumbHeight = 40
}, ref) => {
  const [isDragging, setIsDragging] = useState(false);
  const [thumbPosition, setThumbPosition] = useState(0);
  const [dragStartY, setDragStartY] = useState(0);
  const [startThumbPosition, setStartThumbPosition] = useState(0);
  
  const trackRef = useRef<HTMLDivElement>(null);
  const thumbRef = useRef<HTMLDivElement>(null);
  
  // Use fixed thumb height instead of content-based ratio
  const scrollableContentHeight = Math.max(0, contentHeight - containerHeight);
  const availableTrackHeight = containerHeight - topOffset - bottomOffset;
  const thumbHeight = Math.min(fixedThumbHeight, availableTrackHeight); // Ensure thumb doesn't exceed track height
  const maxThumbPosition = availableTrackHeight - thumbHeight;
  
  // Expose methods via forwardRef
  useImperativeHandle(ref, () => ({
    updateScrollPosition: (scrollTop: number) => {
      const maxScrollTop = Math.max(0, contentHeight - containerHeight);
      if (maxScrollTop > 0) {
        const scrollRatio = scrollTop / maxScrollTop;
        const newThumbPosition = topOffset + (scrollRatio * maxThumbPosition);
        
        // Ensure thumb stays within track bounds
        const safeThumbPosition = Math.max(
          topOffset,
          Math.min(topOffset + availableTrackHeight - thumbHeight, newThumbPosition)
        );
        setThumbPosition(safeThumbPosition);
      }
    },
    getScrollPosition: () => thumbPosition,
    isAtBottom: (threshold: number = 0) => {
      const maxScrollTop = Math.max(0, contentHeight - containerHeight);
      if (maxScrollTop === 0) return true; // No scrollable content, consider at bottom
      
      // Convert thumb position back to scroll position
      const currentScrollRatio = maxThumbPosition > 0 ? (thumbPosition - topOffset) / maxThumbPosition : 0;
      const currentScrollTop = currentScrollRatio * maxScrollTop;
      
      // Check if within threshold distance from bottom
      return (maxScrollTop - currentScrollTop) <= threshold;
    }
  }), [thumbPosition, maxThumbPosition, contentHeight, containerHeight, topOffset, availableTrackHeight, thumbHeight]);
  
  // Update thumb position when position changes
  useEffect(() => {
    if (thumbRef.current && !isDragging) {
      thumbRef.current.style.transform = `translateY(${thumbPosition}px)`;
    }
  }, [thumbPosition, isDragging]);
  
  // Handle mouse events for dragging
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging || !trackRef.current) return;
      
      e.preventDefault();
      const deltaY = e.clientY - dragStartY;
      
      // Ensure thumb never goes beyond track bounds
      const minPosition = topOffset;
      const maxPosition = topOffset + maxThumbPosition;
      const newThumbPosition = Math.max(
        minPosition,
        Math.min(maxPosition, startThumbPosition + deltaY)
      );
      
      // Additional safety check to ensure thumb stays within track
      const safeThumbPosition = Math.max(
        topOffset,
        Math.min(topOffset + availableTrackHeight - thumbHeight, newThumbPosition)
      );
      
      setThumbPosition(safeThumbPosition);
      
      // Calculate corresponding scroll position for main content
      if (onScrollChange && maxThumbPosition > 0) {
        const scrollRatio = (safeThumbPosition - topOffset) / maxThumbPosition;
        const maxScrollTop = Math.max(0, contentHeight - containerHeight);
        const newScrollTop = scrollRatio * maxScrollTop;
        onScrollChange(newScrollTop);
      }
      
      if (thumbRef.current) {
        thumbRef.current.style.transform = `translateY(${safeThumbPosition}px)`;
      }
    };
    
    const handleMouseUp = () => {
      setIsDragging(false);
    };
    
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = 'none'; // Prevent text selection while dragging
    }
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = '';
    };
  }, [isDragging, dragStartY, startThumbPosition, maxThumbPosition, thumbPosition, onScrollChange, contentHeight, containerHeight, topOffset]);
  
  const handleThumbMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
    setDragStartY(e.clientY);
    setStartThumbPosition(thumbPosition);
  };
  
  const handleTrackClick = (e: React.MouseEvent) => {
    if (!trackRef.current || e.target === thumbRef.current) return;
    
    const trackRect = trackRef.current.getBoundingClientRect();
    const clickY = e.clientY - trackRect.top;
    
    // Ensure thumb never goes beyond track bounds
    const newThumbPosition = Math.max(
      topOffset,
      Math.min(topOffset + maxThumbPosition, clickY - thumbHeight / 2)
    );
    
    // Additional safety check to ensure thumb stays within track
    const safeThumbPosition = Math.max(
      topOffset,
      Math.min(topOffset + availableTrackHeight - thumbHeight, newThumbPosition)
    );
    
    setThumbPosition(safeThumbPosition);
    
    // Calculate corresponding scroll position for main content
    if (onScrollChange && maxThumbPosition > 0) {
      const scrollRatio = (safeThumbPosition - topOffset) / maxThumbPosition;
      const maxScrollTop = Math.max(0, contentHeight - containerHeight);
      const newScrollTop = scrollRatio * maxScrollTop;
      onScrollChange(newScrollTop);
    }
  };
  
  // Only show scroller if there's scrollable content
  const hasScrollableContent = scrollableContentHeight > 0;

  if (!hasScrollableContent) {
    return null;
  }

  return (
    <div
      className={`scroller ${className}`}
      style={{
        position: 'absolute',
        right: rightOffset,
        top: 0,
        width: trackWidth,
        height: containerHeight,
        zIndex: 999,
        pointerEvents: 'auto',
        ...style
      }}
    >
      {/* Track */}
      <div
        ref={trackRef}
        style={{
          position: 'absolute',
          top: topOffset,
          left: (trackWidth - thumbWidth) / 2,
          width: thumbWidth,
          height: availableTrackHeight,
          backgroundColor: trackColor,
          borderRadius: thumbWidth / 2,
          cursor: 'pointer'
        }}
        onClick={handleTrackClick}
      />
      
      {/* Thumb */}
      <div
        ref={thumbRef}
        style={{
          position: 'absolute',
          border: 'solid 0.5px #393e47',
          top: 0,
          left: (trackWidth - thumbWidth) / 2,
          width: thumbWidth,
          height: thumbHeight,
          backgroundColor: thumbColor,
          borderRadius: thumbWidth / 2,
          cursor: isDragging ? 'grabbing' : 'grab',
          zIndex: 1,
          transition: isDragging ? 'none' : 'transform 0.1s ease-out',
          willChange: 'transform',
          transform: `translateY(${topOffset}px)`
        }}
        onMouseDown={handleThumbMouseDown}
      />
    </div>
  );
});

Scroller.displayName = 'Scroller';

export default Scroller; 