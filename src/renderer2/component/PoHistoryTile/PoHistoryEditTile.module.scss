.createPoContent {
  height: 1000px;
  background: url(../../assets/New-images/AppBG.svg) no-repeat;

  .formInnerContent {
    position: relative;
    height: calc(100% - 120px);

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 50%;
      height: 1px;
      background: linear-gradient(to right, rgba(255, 255, 255, 0.2), rgba(0, 0, 0, 0));
      z-index: 11;
    }

    .tblWrapper {
      height: 100%;
    }

    .formInputGroup1 {
      display: flex;
      gap: 20px;

      .deliverToContainer {
        flex: 1;
        max-height: 120px;
        padding: 8px 16px;
        border-radius: 12px;
        background-color: rgba(255, 255, 255, 0.04);
        border: 1px solid transparent;
        cursor: pointer;

        &:hover {
          border: solid 1px #459fff;
        }

        &:focus-visible {
          outline: none;
          border: 1px solid #459fff;
        }

        &.boxShadow {
          box-shadow: inset 5px 5px 7.9px -2px #000;
          border: solid 1px transparent;
          background: linear-gradient(#1f2127, #1f2127) padding-box, linear-gradient(to bottom right, rgba(0, 0, 0, 0) 54%, rgba(255, 255, 255, 0.2901960784) 85%) border-box;
          background-color: unset;
          padding: 8px;
        }

        .deliverToLabel {
          width: 100%;
          display: flex;
          flex-direction: column;
          row-gap: 4px;

          .addressInputs {
            width: 100%;
            height: 32px;
            border-radius: 8px;
            background-color: rgba(255, 255, 255, 0.04);
            // padding-left: 12px;
            resize: none;
            border: none;
            color: #459fff;
            caret-color: #459fff;

            &.hideInputBackground {
              background-color: transparent;
              font-family: Inter;
              font-size: 14px;
              font-weight: normal;
              line-height: normal;
              letter-spacing: 1.4px;
              text-align: left;
              color: #fff;
              width: 100%;
              height: 32px;
              display: flex;
              align-items: center;
              padding-left: 12px;
            }

            &:focus {
              box-shadow: none;
            }

            &:focus-within {
              outline: none;
              box-shadow: none;
            }

            &::placeholder {
              font-family: Syncopate;
              font-size: 14px;
              letter-spacing: 0.56px;
              text-align: left;
              color: #616575;
            }

            .stateInput {
              width: 120px;
              background-color: rgba(0, 0, 0, 0.35);
              border: 1px solid rgba(255, 255, 255, 0.1);
              border-radius: 4px;

              .CustomMenu {
                height: 40px;
                color: rgba(255, 255, 255, 0.9);

                &::placeholder {
                  color: rgba(255, 255, 255, 0.4);
                  text-transform: uppercase;
                }
              }

              &:focus-within {
                border-color: #70ff00;
                background-color: rgba(0, 0, 0, 0.45);
              }
            }

            // Zip code input
            input[placeholder="ZIP CODE"] {
              width: 120px;
            }

          }

          .lastAddressFiled {
            display: flex;
            gap: 4px;

            .addressInputsCol1 {
              display: flex;
              flex: 0 336px;
              position: relative;
              z-index: 1;

              input {
                width: 100%;
              }

              &.hideInputBackground {
                background-color: transparent;
                padding-left: 12px;
                color: #fff;
                font-family: Inter;
                font-size: 14px;
                font-weight: normal;
                line-height: normal;
                letter-spacing: 1.4px;
                text-align: left;
                color: #fff;
                height: 32px;
                display: flex;
                align-items: center;
              }
            }

            .addressInputsCol2 {
              position: relative;

              .shape1 {
                width: 13px;
                height: 14px;
                background-color: #3e3f47;
                position: absolute;
                transform: rotate(-390deg);
                top: -6px;
                left: -6px;

                &::before {
                  content: "";
                  position: absolute;
                  width: 100%;
                  height: 100%;
                  background-color: #1f2127;
                  border-radius: 8px;
                  left: -78%;
                  top: 18%;
                }
              }

              .shape2 {
                width: 13px;
                height: 14px;
                background-color: #3e3f47;
                position: absolute;
                transform: rotate(-113deg);
                top: -6px;
                right: -8px;

                &::before {
                  content: "";
                  position: absolute;
                  width: 100%;
                  height: 100%;
                  background-color: #1f2127;
                  border-radius: 8px;
                  left: -57%;
                  top: 20%;
                }
              }

              &.selectShade {
                background-color: #3e3f47;
                height: 36px;
                top: -4px;
                position: relative;
                border-radius: 0px 0px 8px 8px;
              }

              &.hideInputBackground {
                background-color: transparent;
                padding-left: 12px;
                color: #fff;
                font-family: Inter;
                font-size: 14px;
                font-weight: normal;
                line-height: normal;
                letter-spacing: 1.4px;
                text-align: left;
                color: #fff;
                height: 32px;
                display: flex;
                align-items: center;
              }

              .stateWrapper {
                height: 34px;
                border-radius: 8px;
                background-color: #3e3f47;
                display: flex;
                align-items: center;
                padding: 4px 0px 0px 5px;

                svg {
                  position: absolute;
                  right: 2px;
                  top: 8px;
                }

                input {
                  width: 63px;
                  height: 24px;
                  padding: 5px 2.4px 5px 4.5px;
                  border-radius: 6px;
                  background-color: #111217;
                  font-family: Syncopate;
                  font-size: 14px;
                  font-weight: normal;
                  line-height: 1;
                  letter-spacing: 0.56px;
                  text-align: left;
                  color: #459fff;

                  &::placeholder {
                    color: rgba(97, 101, 117, 0.5);
                  }
                }
              }
            }

            .addressInputsCol2,
            .addressInputsCol3 {
              display: flex;
              flex: 0 90px;

              input {
                width: 100%;
                padding: 12px;
              }

              &.hideInputBackground {
                background-color: transparent;
                padding-left: 12px;
                color: #fff;
                font-family: Inter;
                font-size: 14px;
                font-weight: normal;
                line-height: normal;
                letter-spacing: 1.4px;
                text-align: left;
                color: #fff;
                height: 32px;
                display: flex;
                align-items: center;
              }
            }

            .addressInputsCol3 {
              position: relative;
              z-index: 1;
              flex: 0 84px;

              .errorInput {
                color: white;
                box-shadow: none;
              }
            }
          }
        }

      }

      .uploadBillContainer {
        width: 180px;
        height: 120px;
        flex-grow: 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 8px;
        border-radius: 12px;
        background-color: rgba(255, 255, 255, 0.04);
        cursor: pointer;
        transition: all 0.2s ease;

        .uploadIcon2 {
          display: none;
        }

        &.disabled {
          cursor: not-allowed;
          opacity: 0.5;

          &:hover {
            background: rgba(255, 255, 255, 0.04);

            .uploadLabel {
              font-weight: normal;
              color: #616575;
            }

            .uploadIcon2 {
              display: none;
            }

            .uploadIcon1 {
              display: block;
            }
          }
        }

        &:hover {
          background: #fff;

          .uploadLabel {
            color: #1b1b21;
            font-weight: bold;
          }

          .uploadIcon2 {
            display: block;
          }

          .uploadIcon1 {
            display: none;
          }
        }

        &:focus-visible {
          outline: none;
          background: #fff;

          .uploadLabel {
            color: #1b1b21;
            font-weight: bold;
          }

          .uploadIcon2 {
            display: block;
          }

          .uploadIcon1 {
            display: none;
          }
        }

        .uploadIcon {
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .uploadLabel {
          font-family: Syncopate;
          font-size: 14px;
          font-weight: normal;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.2;
          letter-spacing: 0.56px;
          text-align: center;
          color: #616575;
        }
      }

      .lblInput {
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.6;
        text-align: left;
        color: #fff;
        width: 152px;
        height: 34px;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        padding: 6px 4px 6px 10px;
        border: solid 0.5px #000;
        background-color: rgba(0, 0, 0, 0.25);
        border-radius: 4px 0px 0px 4px;
        border-right: 0px;

        .questionIcon {
          display: flex;
          align-items: center;
          margin-left: auto;
          transition: all 0.1s;

          .questionIcon2 {
            display: none;
          }

          .questionIcon3 {
            display: none;
          }

          .questionIcon4 {
            display: none;
          }

          &:hover {
            .questionIcon1 {
              display: none;
            }

            .questionIcon2 {
              display: block;
            }
          }
        }
      }

      .inputSection {
        padding: 13px 16px;
        border-radius: 12px;
        background-color: rgba(255, 255, 255, 0.04);
        width: 250px;
        height: 40px;
        font-family: Syncopate;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: 0.56px;
        text-align: left;
        color: #616575;
        border: none;
        flex: 1;

        &.paddingLR0 {
          padding-left: 0px;
          padding-right: 0px;
        }

        &.cityInput {
          flex: 0 0 96px;
        }

        &.stateInput {
          flex: 0 85px;
        }

        &.zipCodeInput {
          flex: 0 0 72px;
          padding: 6px 3px 6px 6px;

        }

        &.bdrRadius0 {
          border-radius: 0px;
        }

        &.bdrRight0 {
          border-right: 0px;
        }

        .chooseOneContainer {
          width: 100%;
          display: flex;
          flex-direction: column;

          .chooseOneHeading {
            font-family: Inter;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 4px;
          }

          .radioContainer {
            display: flex;
            gap: 20px;
            align-items: center;

            .radioLabel {
              display: flex;
              align-items: center;
              gap: 8px;
              color: #fff;
              font-family: Inter;
              font-size: 14px;
              cursor: pointer;

              .radioInput {
                appearance: none;
                width: 16px;
                height: 16px;
                border: 2px solid rgba(255, 255, 255, 0.5);
                border-radius: 50%;
                margin: 0;
                position: relative;
                cursor: pointer;

                &:checked {
                  border-color: #70ff00;

                  &::after {
                    content: '';
                    position: absolute;
                    width: 8px;
                    height: 8px;
                    background-color: #70ff00;
                    border-radius: 50%;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                  }
                }

                &:hover {
                  border-color: #70ff00;
                }
              }
            }
          }
        }

        input {
          width: 100%;
          font-family: Inter;
          font-size: 14px;
          font-weight: normal;
          line-height: 1.6;
          text-align: left;
          color: #fff;
          border: 0px;
          background-color: transparent;
          padding: 0px;

          &:focus {
            outline: none;
            box-shadow: none;
          }

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
            font-weight: normal;
          }
        }
      }

      .errorInput {
        border-color: #ff3b3b !important;

        &:focus {
          border-color: #ff3b3b !important;
        }
      }

      &.FormInputGroupError {
        .lblInput {
          border: solid 0.5px #f00;
          background-color: #f00;
          cursor: pointer;
          white-space: nowrap;
        }

        .borderOfError {
          border: solid 0.5px #f00;
        }
      }

      select {
        background: transparent;
        border: 0;
        color: rgba(255, 255, 255, 0.5);
        height: 100%;
        width: 100%;
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.6;
        text-align: left;
        color: #fff;
      }

      &:focus-within {
        .lblInput {
          background-color: #70ff00;
          border: solid 0.5px #70ff00;
          color: #000;

          .questionIcon {
            display: flex;
            align-items: center;
            margin-left: auto;
            transition: all 0.1s;

            .questionIcon3 {
              display: none;
            }

            .questionIcon1 {
              display: none;
            }

            .questionIcon2 {
              display: none;
            }

            .questionIcon4 {
              display: block;
            }

            &:hover {
              .questionIcon4 {
                display: none;
              }

              .questionIcon1 {
                display: none;
              }

              .questionIcon2 {
                display: none;
              }

              .questionIcon3 {
                display: block;
              }
            }
          }
        }

        .inputSection {
          height: 34px;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: flex-start;
          padding: 6px 8px 6px 10px;
          border: solid 0.5px #70ff00;
          background-color: rgba(0, 0, 0, 0.2);
          flex: 1;

          &.paddingLR0 {
            padding-left: 0px;
            padding-right: 0px;
          }

          &.cityInput {
            flex: 0 0 96px;
          }

          &.stateInput {
            flex: 0 85px;
          }

          &.zipCodeInput {
            flex: 0 0 72px;
            padding: 6px 3px 6px 6px;

          }

          &:last-child {
            border-left: 0px;
          }

          input {
            width: 100%;
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.6;
            text-align: left;
            // color: #70ff00;
            border: 0px;
            background-color: transparent;
            padding: 0px;

            &:focus {
              outline: none;
              box-shadow: none;
            }

            &::placeholder {
              font-weight: normal;
            }
          }
        }
      }
    }

    .formInputGroupFocus {
      .lblInput {
        background-color: #70ff00;
        border: solid 0.5px #70ff00;
        color: #000;

        .questionIcon {
          display: flex;
          align-items: center;
          margin-left: auto;
          transition: all 0.1s;

          .questionIcon3 {
            display: none;
          }

          .questionIcon1 {
            display: none;
          }

          .questionIcon2 {
            display: none;
          }

          .questionIcon4 {
            display: block;
          }

          &:hover {
            .questionIcon4 {
              display: none;
            }

            .questionIcon1 {
              display: none;
            }

            .questionIcon2 {
              display: none;
            }

            .questionIcon3 {
              display: block;
            }
          }
        }
      }

      .inputSection {
        height: 34px;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: flex-start;
        padding: 6px 8px 6px 10px;
        border: solid 0.5px #70ff00;
        background-color: rgba(0, 0, 0, 0.2);
        flex: 1;

        &.paddingLR0 {
          padding-left: 0px;
          padding-right: 0px;
        }

        &.cityInput {
          flex: 0 0 96px;
        }

        &.stateInput {
          flex: 0 85px;
        }

        &.zipCodeInput {
          flex: 0 0 72px;
          padding: 6px 3px 6px 6px;

        }

        &:last-child {
          border-left: 0px;
        }

        input {
          width: 100%;
          font-family: Inter;
          font-size: 14px;
          font-weight: normal;
          line-height: 1.6;
          text-align: left;
          border: 0px;
          background-color: transparent;
          padding: 0px;

          &:focus {
            outline: none;
            box-shadow: none;
          }

          &::placeholder {
            font-weight: normal;
          }
        }
      }
    }
  }

  .totalWeight {
    font-family: Inter;
    font-size: 14px;
    line-height: 1.4;
    text-align: left;
    color: #fff;
    padding-left: 40px;
    padding-bottom: 3px;
    border-bottom: 0.2px solid #fff;
    margin-top: -12px;
  }

  .lineWeight {
    font-family: Inter;
    font-size: 14px;
    line-height: 1.4;
    text-align: left;
    color: rgba(255, 255, 255, 0.75);
  }

  .addMoreLine {
    position: relative;
    margin-top: 16px;
    display: flex;
    align-items: center;

    button {
      background-color: transparent;
      border: 0px;
      transition: all 0.1s;
      position: relative;
      top: 1.5px;

      .addLineHover {
        display: none;
      }

      &:hover {
        .addLine {
          display: none;
        }

        .addLineHover {
          display: inline-block;
        }
      }

      &:focus-visible {
        border: 1px solid #70ff00;
      }
    }

    span {
      width: 100%;
      display: flex;
      border-top: 0.2px solid #fff;
      position: absolute;
      z-index: -1;
    }
  }

  .removeLine {
    position: relative;
    margin-top: 16px;

    button {
      z-index: 1;
      position: absolute;
      top: -13px;
      left: 0;
      background-color: transparent;
      border: 0px;
      transition: all 0.1s;

      .removeLineHoverIcon {
        display: none;
      }

      &:hover {
        .removeLineIcon {
          display: none;
        }

        .removeLineHoverIcon {
          display: inline-block;
        }
      }

      &:focus-visible {
        border: 1px solid #70ff00;
      }
    }

  }

  .totalAmt {
    margin-top: 8px;
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;

    .featureActions {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .refreshNetTerm {
        opacity: 0.7;
        font-family: Inter;
        font-size: 12px;
        line-height: 1.2;
        text-align: left;
        color: #fff;
        display: flex;
        align-items: center;
        gap: 2px;
        white-space: nowrap;

        &:hover {
          opacity: 1;
        }
      }
    }

    .saleTax {
      font-family: Inter;
      font-size: 18px;
      line-height: 1.6;
      text-align: right;
      color: #fff;

      .questionIcon {
        vertical-align: middle;
        margin-left: 6px;
        transition: all 0.1s;

        .questionIcon2 {
          display: none;
        }

        &:hover {
          .questionIcon1 {
            display: none;
          }

          .questionIcon2 {
            display: inline-block;
          }
        }
      }
    }

    .totalPurchase {
      font-family: Inter;
      font-size: 24px;
      font-weight: 600;
      line-height: 1.6;
      text-align: right;
      color: #fff;
    }

    table {
      width: 100%;
      display: flex;
      justify-content: end;
      white-space: nowrap;

      tr {
        td {
          text-align: right;
          padding: 0px 6px;

          .prodId {
            display: inline-block;
          }
        }
      }
    }
  }

  .btnOfCreatePo {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-top: 12px;

    .selectedpayment {
      font-family: Inter;
      font-size: 24px;
      font-weight: 600;
      line-height: 1.6;
      color: #fff;
      width: 100%;
      height: 54px;
      border-radius: 4px;
      border: solid 1px rgba(255, 255, 255, 0.1);
      background-color: rgba(255, 255, 255, 0.1);
      cursor: pointer;

      .selectPaymentMethod {
        padding: 0px;
        font-family: Inter;
        font-size: 18px;
        line-height: 1.6;
        text-align: left;
        color: #fff;
        height: 50px;
        width: 100%;

        .MuiSelect-select {
          display: flex;
          justify-content: center;
        }

        svg {
          right: 10px;
          top: calc(50% - 0.6em);
          transform: unset;
          color: #fff;
        }

        fieldset {
          border: 0;
        }
      }
    }

    button {
      width: 100%;
      height: 54px;
      padding: 8px 0;
      border-radius: 4px;
      border: solid 1px rgba(255, 255, 255, 0.1);
      background-color: rgba(255, 255, 255, 0.1);
      cursor: pointer;
      transition: all 0.1s;

      &:hover {
        background-color: #70ff00;

        &.placeOrder {
          color: #000;
        }
      }

      &.placeOrder {
        font-family: Inter;
        font-size: 24px;
        font-weight: normal;
        line-height: 1.6;
        color: #fff;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        border: solid 1px rgba(255, 255, 255, 0.1);
        background-color: rgba(255, 255, 255, 0.1);

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);

          &.placeOrder {
            color: #fff;
          }
        }
      }
    }
  }

  .textOfCreatePo {
    width: 100%;
    font-size: 14px;
    font-family: Inter;
    line-height: 1.6;
    font-weight: 300;
    text-align: center;
    color: #fff;
    margin-top: 12px;
  }

  .backBtnMain {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 40px;
    background-color: #000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px 0 40px;
    z-index: 100;
    border-bottom-left-radius: 21px;
    border-bottom-right-radius: 21px;

    .savePOGoBack {
      font-family: Syncopate;
      font-size: 11.8px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: -0.48px;
      text-align: center;
      color: rgba(255, 255, 255, 0.6);

      &:hover {
        color: rgba(255, 255, 255, 0.9);
      }

      &:focus-visible {
        color: rgba(255, 255, 255, 0.9);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
          color: rgba(255, 255, 255, 0.6);
        }

        &:focus-visible {
          color: rgba(255, 255, 255, 0.6);
        }
      }
    }

    .cancelPOGoBack {
      font-family: Syncopate;
      font-size: 10px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: 0.4px;
      text-align: center;
      color: rgba(255, 255, 255, 0.5);
      margin-right: 237px;

      &:hover {
        color: rgba(255, 255, 255, 0.9);
      }

      &:focus-visible {
        color: rgba(255, 255, 255, 0.9);
      }

    }
  }

}

.selectPaymentMethodPaper.selectPaymentMethodPaper {
  padding: 3px 4px 8px 12px;
  backdrop-filter: blur(24px);
  background-color: #ffffff4c;
  box-shadow: 0px 8px 30px #000000cc;
  margin-top: 9px;
  overflow: hidden;
  background: url(../../assets/images/DropDownBG.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;

  ul {
    overflow: auto;
    max-height: 260px;
    padding-right: 8px;

    &::-webkit-scrollbar {
      width: 8px;
      height: 6px;
    }

    

    li {
      font-family: Inter;
      font-size: 16px;
      font-weight: normal;
      line-height: 1.57;
      text-align: left;
      color: #fff;
      border-radius: 2px;
      margin-bottom: 3px;

      i {
        font-size: 12px;
      }

      &:hover {
        background-color: #fff;
        color: #000;
      }

      &[aria-selected="true"] {
        background-color: #fff;
        color: #000;
        border-radius: 2px;
      }

    }

    .Mui-selected.Mui-selected {
      background-color: transparent;
    }
  }
}

.selectUomPaper.selectUomPaper {
  padding: 4px;
  border-radius: 6px;
  -webkit-backdrop-filter: blur(22.4px);
  backdrop-filter: blur(22.4px);
  background-color: rgba(128, 130, 140, 0.28);
  box-shadow: none;

  ul {
    overflow: auto;
    max-height: 260px;
    padding: 0;

    &::-webkit-scrollbar {
      width: 6px;
    }

    li {
      text-transform: uppercase;
      margin-bottom: 1px;
      padding: 6px;
      border-radius: 5px;
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: center;
      color: rgba(255, 255, 255, 0.6);

      &[aria-selected="true"] {
        background-color: rgba(255, 255, 255, 0.2);
        font-weight: bold;
        color: #fff;
      }

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
        font-weight: bold;
        color: #fff;
      }

    }

    .Mui-selected.Mui-selected {
      background-color: rgba(255, 255, 255, 0.2);
      font-weight: bold;
      color: #fff;
    }
  }
}


.autocompleteDescPanel {
  border-radius: 4px;
}

.autocompleteDescInnerPanel.autocompleteDescInnerPanel {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  box-shadow: inset 4px 4px 10.1px 0 #000;
  border-radius: 0px 0px 13px 13px;
  
}

.noOptionPanel.noOptionPanel {
  display: none;
  font-family: Inter;
  font-size: 14px;
  font-weight: 300;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: left;
  color: #eaecf0;
}

.visibleNoOption.visibleNoOption {
  display: block;
}

.autocompletePaper {
  overflow: auto;
  border-radius: 0px 0px 13px 13px;
  max-height: 400px;
background: url(../../assets/New-images/Mian-Dropdown-BG.svg) #1b1c21 no-repeat;
  background-position: bottom right;
  &::-webkit-scrollbar {
    width: 6px;
  }
}

.listAutoComletePanel.listAutoComletePanel {
  width: 100%;
  max-height: 100%;
  padding: 6px 4px 6px 10px;
  margin-top: 4px;
  overflow: hidden;
  &::-webkit-scrollbar {
    width: 6px;
  }

  span {
    padding: 8px;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 8px;
    font-family: Inter;
    font-size: 14px;
    font-weight: 300;
    line-height: 1.4;
    letter-spacing: 0.98px;
    text-align: left;
    color: #eaecf0;
    text-transform: uppercase;

    &:hover {
      border-radius: 10px;
      background-image: linear-gradient(335deg, #eaecf0 0%, #9b9eac 100%);
      color: #0f0f14;
    }

    &[aria-selected="true"] {
      border-radius: 10px;
      background-image: linear-gradient(335deg, #eaecf0 0%, #9b9eac 100%);
      color: #0f0f14;

      &.Mui-focused {
        background-image: linear-gradient(335deg, #eaecf0 0%, #9b9eac 100%);
        background-color: transparent;
      }
    }
  }
}

.Dropdownpaper.Dropdownpaper {
  padding: 3px 4px 8px 8px;
  -webkit-backdrop-filter: blur(30px);
  backdrop-filter: blur(30px);
  box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
  background-color: rgba(0, 0, 0, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 7px;
  overflow: hidden;
  width: 66px;
  border-radius: 4px;
  background: url(../../assets/images/DropDownBG.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;

  &.DropdownDeliveryDate {
    margin-top: 8px;
  }

  ul {
    overflow: auto;
    max-height: 230px;
    padding-right: 4px;
    padding-top: 0px;
    padding-bottom: 0px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    

    li {
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.4;
      text-align: left;
      color: #fff;
      border-radius: 2px;
      padding: 8px 12px;
      margin-bottom: 2px;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: #fff;
      }

      &.Mui-selected {
        background-color: rgba(112, 255, 0, 0.2);
        color: #fff;
      }
    }
  }
}


.ErrorDialog {
  .dialogContent {
    max-width: 330px;
    width: 100%;
    min-height: 230px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 30px 34px 30px 34px;
    object-fit: contain;
    border-radius: 10px;
    -webkit-backdrop-filter: blur(24px);
    backdrop-filter: blur(24px);
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    background-color: rgba(0, 0, 0, 0.72);
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: center;
    color: #fff;

    p {
      margin-bottom: 20px;
    }


    .submitBtn {
      height: 40px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 10px 24px;
      border-radius: 4px;
      border: solid 0.5px #fff;
      background-color: transparent;
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.4;
      text-align: center;
      color: #fff;
      transition: all 0.1s;

      &:hover {
        background-color: #70ff00;
        border: solid 0.5px #70ff00;
        color: #000;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
          border: solid 0.5px #fff;
          background-color: transparent;
          color: #fff;
        }
      }
    }


  }

}

.w100 {
  width: 100%;
  position: relative;
}


.headerNoteCreatePO {
  height: 72px;
  padding: 8px;
  border-radius: 6px;
  background-color: rgba(255, 255, 255, 0.15);
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-bottom: 10px;
  position: relative;

  .headerNoteText {
    font-family: Inter;
    font-size: 12px;
    line-height: 1.4;
    text-align: center;
    color: #fff;
  }

  .marginTop8 {
    margin-top: 8px;
  }

  .leftIcon {
    position: absolute;
    left: 8px;
    top: 20px;
  }

  .rightIcon {
    position: absolute;
    right: 8px;
    top: 20px;
  }

  svg {
    width: 32px;
    height: 32px;
  }
}

.domesticMaterialCheckbox {
  margin-top: 5px;
  display: flex;
  padding-bottom: 10px;

  .lblCheckbox {
    display: flex;
    align-items: center;
    white-space: nowrap;
    padding-left: 21px;
    font-size: 12px;

    &:focus-within {
      .checkmark {
        border: solid 1px #70ff00;
      }
    }

    .domesticMaterialTex {


      font-family: Inter;
      font-size: 14px;
      line-height: 1.6;
      text-align: left;
      color: rgba(255, 255, 255, 0.75);
    }

    .checkmark {
      width: 15px;
      height: 15px;
      border-radius: 1.7px;
      border: solid 0.7px #3b4665;
      background-color: #ebedf0;
      top: 4px;

      &::after {
        left: 5px;
        top: 2px;
        width: 3px;
        height: 6px;

      }
    }

    input:checked~.checkmark {
      background-color: #70ff00;
      border: solid 0.7px #70ff00;
    }

  }

  .div50 {
    width: 280px;
  }

  .hiddenCheckbox:focus+.customNumberToggle,
  .hiddenCheckbox:focus-visible+.customNumberToggle {
    outline: 1px solid #459fff;
    box-shadow: 0 0 3px rgba(69, 159, 255, 0.5);
    opacity: 1;
  }
}

.questionIconDesc {
  vertical-align: middle;
  margin-left: 6px;
  transition: all 0.1s;
  position: absolute;
  top: 12px;
  right: 90px;
  cursor: pointer;

  .questionIcon2 {
    display: none;
  }

  &:hover {
    .questionIcon1 {
      display: none;
    }

    .questionIcon2 {
      display: inline-block;
    }
  }
}

.radioGroupContainer {
  display: flex;
  text-align: center;
  cursor: pointer;
  justify-content: center;
  position: relative;

  .chosseOneIcon {
    position: absolute;
    top: -19px;
  }

  .radioButton {
    width: 90px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    background-color: rgba(255, 255, 255, 0.04);

    &:not(.disableBidBuyNowBtn):hover {
      color: #459fff;
    }

    &.selected {
      font-family: Syncopate;
      font-size: 14px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1;
      letter-spacing: 0.56px;
      text-align: left;
      color: #459fff;
    }

    &.buySelected {
      background: url(../../assets/New-images/BUYInputBG.svg);
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      border-left: none;
    }

    &.bidSelected {
      background: url(../../assets/New-images/BIDInputBG.svg);
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
    }
  }

  .disableBidBuyNowBtn {
    cursor: not-allowed;
  }

  .radioButtonLeft {
    border-right: solid 1.5px #323336;
    border-top-left-radius: 13px;
    border-bottom-left-radius: 13px;

    &:focus-visible {
      outline: none;

      &.radioButton {
        color: #459fff;
      }
    }
  }

  .radioButtonRight {
    border-left: solid 2px #1c1d23;
    border-top-right-radius: 13px;
    border-bottom-right-radius: 13px;

    &:focus-visible {
      outline: none;

      &.radioButton {
        color: #459fff;
      }
    }
  }

  .hiddenRadio {
    display: none;
  }
}

.inputfiled.inputfiled {
  padding: 6px 16px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.04);
  width: 250px;
  height: 40px;
  border: none;
  flex: 1;

  &:focus {
    border: none;
    color: #459fff;
    caret-color: #459fff;
  }

  &.pOInput {
    border: 1px solid transparent;

    &:focus {
      background: url(.././../assets/New-images/POInputBG.svg);
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      border: 1px solid transparent;
      background-position: bottom right;
    }
  }

  &::placeholder {
    font-family: Syncopate;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: 0.56px;
    text-align: left;
    color: #616575;
  }

}

.formInputGroup {
  position: sticky;
  top: 0;
  z-index: 0;
  padding: 32px 40px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  font-family: Syncopate;
  font-size: 14px;
  line-height: 1.2;
  letter-spacing: 0.56px;
  color: #616575;
  // background-image: linear-gradient(79deg, #0f0f14 50%, #393e47 135%);
}


.createPOContainer {
  // overflow-y: auto;
  height: 100%;
  max-height: calc(100% - 40px);
  position: relative;
  scroll-behavior: smooth;


  &::-webkit-scrollbar {
    width: 0px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    border-image-source: radial-gradient(circle at 31% -2%, #fff, rgba(255, 255, 255, 0) 23%);
    background-image: linear-gradient(to bottom, #191a20, #191a20), radial-gradient(circle at 31% -2%, #fff, rgba(255, 255, 255, 0) 23%);
  }
}

.removeFooter {
  max-height: 100%;
}

.deliverByContainer {
  width: 100%;
  max-width: 250px;
  position: relative;
}

.deliverByButton1 {
  width: 100%;
  height: 40px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.04);
  display: flex;
  border: 1px solid transparent;
  font-family: Inter;
  font-size: 14px;
  letter-spacing: 0.56px;
  color: #fff;

  &:hover {
    border-color: #459fff;
  }

  .leftSideSpan {
    width: 72px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.04);
    border-top-left-radius: 13px;
    border-bottom-left-radius: 13px;
  }

  .rightSideSpan {
    color: #fff;
    width: calc(100% - 72px);
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
  }
}

.deliverByButton2 {
  width: 100%;
  height: 40px;
  padding: 12px 20px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.04);
  font-family: Syncopate;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: 0.56px;
  text-align: left;
  color: #616575;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: #459fff;
  }

  &:focus-visible {
    outline: none;
    border-color: #459fff;
  }
}

.datePickerPopper {
  background-color: #1a1a1a;
  color: white;
  border-radius: 8px;
  border: 1px solid #30363d;
}

.headerContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-image: linear-gradient(164deg, #0f0f14 -30%, #393e47 230%);
  padding: 8px 40px;
  width: 100%;
  position: absolute;
  top: 0px;
  left: 0;
  right: 0;
  z-index: 2;
  opacity: 0;
  height: 40px;
  cursor: pointer;

  .headerItem {
    &:nth-child(1) {
      width: 194px;
    }

    &:nth-child(2) {
      width: 115px;
    }

    &:nth-child(3) {
      width: 38px;
    }

    &:nth-child(4) {
      width: 141px;
    }

    font-family: Inter;
    font-size: 15px;
    font-weight: normal;
    letter-spacing: 0.6px;
    text-align: left;
    color: rgba(69, 159, 255, 0.8);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;

    &:hover {
      color: #459fff;
      font-weight: bold;
    }
  }
}

.showHeader {
  opacity: 1;
}


.poDescriptionFirstLine {
  &::first-line {
    font-weight: 500;
  }

  &.poDescriptionFirstLine1.poDescriptionFirstLine1 {
    padding-right: 0px !important;

    &::first-line {
      letter-spacing: 0;
    }
  }
}

.poDescriptionDivFocused {
  box-shadow: inset 4px 4px 10.1px 0 #000;

  .partNumberFiled {
    box-shadow: inset 4px 4px 10.1px 0 #000;

    input {
      &::placeholder {
        // color: #616575;
        font-weight: bold;
      }
    }
  }

  .poDescription {
    color: #459fff;
  }
}

.formInputGroup {
  will-change: transform, opacity;
  transform-origin: top;
}

.addPoLineTable {
  will-change: transform;
}

.createPOContainer {
  position: relative;
  height: 100%;
  overflow: auto;
}

.productDescription {
  width: 100%;
  display: inline-flex;
}

.miscellaneousText.miscellaneousText {
  padding: 8px 4px 8px 4px !important;

  .liFisrtLine.liFisrtLine {
    letter-spacing: 0;
  }
}


.prodId {
  display: flex;
  align-items: center;
  position: relative;

  .activeInactiveDot {
    cursor: pointer;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    position: absolute;
    top: 18px;
    left: -19px;
  }

  .activeInactiveDot.active {
    background-color: #43f776;
  }

  .activeInactiveDot.inactive {
    background-color: #ff0000;
  }

  .lineNumberContainer {
    display: flex;
    flex-direction: column;
    gap: 5px;
    align-items: center;
    justify-content: center;
    text-align: center;

    .hiddenCheckbox {
      display: none;
    }

    .customNumberToggle {
      background-image: url(../../assets/New-images/Product-Id-BG.svg);
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      border-radius: 5px;
      border: solid 0.5px rgba(255, 255, 255, 0.16);
      width: 30px;
      height: 30px;
      font-family: Syncopate;
      font-size: 16px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1;
      letter-spacing: 1.12px;
      text-align: left;
      color: #9b9eac;
      display: flex;
      justify-content: center;
      align-items: center;
      padding-top: 3px;
      cursor: pointer;
      transition: all 0.2s ease;

      &.active {
        background-image: url(../../assets/New-images/Usa-Only-Without-Text.svg);
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        color: #0f0f14;
        border: none;
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      &:focus-visible {
        outline: 1px solid #459fff;
        box-shadow: 0 0 3px rgba(69, 159, 255, 0.5);
      }
    }

    .usaOnlyText {
      font-family: Syncopate;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.5);
      font-weight: normal;

      &.visibilityHidden {
        visibility: hidden;
      }
    }
  }

  .domesticCheckText {
    position: absolute;
    top: 44px;
    left: -28px;
  }
}

.skippedPlaceHolderText {
  position: absolute;
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.98px;
  color: #616575;
  padding-left: 16px;
}

.poDescriptionDiv {
  width: 292px;
  height: 162px;
  border-radius: 13px;
  background-color: #23242a;

  &:focus-within {
    // box-shadow: inset 4px 4px 10.1px 0 #000;
    background-color: transparent;
    background: url(../../assets/New-images/Upload_BOM_Description.svg) no-repeat;
    background-position: bottom right;

    .partNumberFiled {
      box-shadow: inset 4px 4px 10.1px 0 #000;
      width: calc(100% - 1px);
    }

    .poDescription {
      color: #459fff;
    }
  }

  &.disabled {
    cursor: not-allowed;
  }

  .poDescription {
    background-color: transparent;
    border: 0;
    width: 100%;
    height: 117px;
    resize: none;
    caret-color: #459fff;
    font-family: Inter;
    font-size: 14px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: 0.98px;
    text-align: left;
    color: #eaecf0;
    padding-left: 16px;
    padding-right: 16px;
    padding-top: 16px;
    overflow: hidden;

    &[readonly] {
      text-transform: uppercase;
    }



    &::placeholder {
      font-family: Syncopate;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: normal;
      letter-spacing: 0.56px;
      text-align: left;
      color: #b5b6bb;
    }

    &:focus-within {
      outline: none;
      box-shadow: none;

      &::placeholder {
        font-family: Syncopate;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: 0.56px;
        text-align: left;
        color: #616575;
        line-height: 22px;

        &::first-line {
          font-weight: normal;
        }
      }
    }

    &:disabled {
      cursor: not-allowed;

      &::placeholder {
        font-family: Syncopate;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: 0.56px;
        text-align: left;
        color: #b5b6bb;
        line-height: 21px;
      }
    }
  }

  .poDescriptionOpen {
    height: 47px;
  }

  .partNumberFiled {
    width: 100%;
    height: 40px;
    background-color: transparent;
    border: none;
    padding: 0px 16px 0px 16px;
    font-family: Syncopate;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: 0.56px;
    text-align: left;
    color: #616575;
    border-bottom-left-radius: 13px;
    border-bottom-right-radius: 13px;
    text-transform: uppercase;

    input {
      font-family: Syncopate;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: normal;
      letter-spacing: 0.56px;
      text-align: left;
      color: #616575;
      caret-color: #459fff;
      width: 100%;
      height: 100%;
      border: none;
      background: transparent;
      padding: 0px 0px 0px 1px;

      &:disabled {
        cursor: not-allowed;
      }

      &::placeholder {
        color: #616575;
      }

      &:focus-within {
        border: none;
        outline: none;
        box-shadow: none;

        &::placeholder {
          color: #616575;
          font-weight: bold;
        }
      }
    }

    span {
      position: absolute;
      right: 10px;
      top: 4px;
    }

  }

}

.descriptionModeDisabled {
  position: relative;
  width: 100%;

  .textareaOverlay {
    position: absolute;
    z-index: 99;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    border: 0;
    padding-left: 16px;
    padding-right: 10px;
    padding-top: 16px;
    overflow: hidden;
    border-radius: 13px;
    background-color: #23242a;
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.4;
    letter-spacing: 0.98px;
    text-align: left;
    color: #616575;
    cursor: pointer;

    .childCount {
      font-family: Syncopate;
      font-weight: bold;
      color: #fff;
    }

    .matches {
      font-family: Syncopate;
      font-size: 12px;
      line-height: 1.4;
      letter-spacing: 0.84px;
      color: #fff;
      text-transform: uppercase;
    }
  }
}

.poQty,
.poPerUm {

  .errorInput {
    border: 1px solid red;

    &:focus-within {
      border: solid 1px red;
      outline: none;
      box-shadow: none;
    }
  }


}

.poQty {
  color: rgba(255, 255, 255, 0.6);
  font-weight: normal;
}

.poQty {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;

  .poQtyValue {
    border-radius: 10px;
    background-color: rgba(255, 255, 255, 0.04);
    width: 95px;
    height: 45px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Inter;
    font-size: 15px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #fff;
    border: 0;
  }

  input {
    width: 95px;
    height: 45px;
    font-family: Inter;
    font-size: 16px;
    line-height: 1.3;
    letter-spacing: -0.18px;
    text-align: center;
    color: #fff;
    caret-color: #fff;

    &:focus {
      background: url(../../assets/New-images/POInputActive.svg) no-repeat transparent;
      background-position: bottom right;
      box-shadow: inset 4px 4px 10.1px 0 #000;
      border: 0;
    }

    &:disabled {
      cursor: not-allowed;
    }
  }

  .qtyInputUnit {
    display: flex;
    align-items: center;
    margin-left: 4px;
    position: absolute;
    right: -4px;
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: right;
    color: #fff;
  }


}

.poQtyBOM {
  input {
    text-align: center;
    padding: 4px 12px;
  }
}

.errorQty {
  box-shadow: inset 2.2px 2.2px 2.2px 0 #000;
  background-image: linear-gradient(159deg, #720d16 -254%, #ff4859 98%), linear-gradient(37deg, #8c8b99 168%, #2f2e33 19%);
}

.poPerUm {
  font-family: Inter;
  font-size: 14px;
  text-align: center;
  color: #fff;
  display: flex;
  height: 45px;
  align-items: center;
  justify-content: center;
  position: relative;

  .selectUom {
    position: absolute;
    right: 0px;
  }
}


.extendedValue {
  font-family: Inter;
  font-size: 14px;
  text-align: right;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: right;
  height: 45px;
}

.autocompleteDescPanelBom.autocompleteDescPanelBom {
  border-radius: 0px 0px 13px 13px;
  width: calc(100% - 1px);
  background: url(../../assets/New-images/Mian-Dropdown-BG.svg) #1b1c21 no-repeat;
  background-position: bottom right;
}

.autocompleteDescInnerPanelBom.autocompleteDescInnerPanelBom {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  box-shadow: inset 4px 4px 10.1px 0 #000;
  border-radius: 0px 0px 13px 13px;
  flex-direction: column;
  width: calc(100% - 2px);
}

.matchesfoundSearch {
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.98px;
  text-align: left;
  color: #616575;
  display: inline-block;
  width: 100%;

  .textStyle1 {
    font-weight: bold;
    color: #fff;
    display: inline-block;
    margin-right: 3px;
  }

  .textStyle2 {
    color: #fff;
    display: inline-block;
  }

  br {
    display: inline-block;
  }

}

.noOptionListPanelBom.noOptionListPanelBom {
  background: transparent !important;
  box-shadow: unset !important;
}

.noOptionListInnerPanelBom {
  border-radius: 0px !important;
  box-shadow: unset !important;
}

.noOptionPanelBom1.noOptionPanelBom1 {
  padding: 0px !important;
}

.noOptionPanelBom.noOptionPanelBom {
  display: none;
}


.visibleNoOptionBom.visibleNoOptionBom {
  display: flex;
  align-items: center;
  font-family: Inter;
  font-size: 14px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: left;
  color: #eaecf0;
  width: 100%;
  height: 50px;
  flex-grow: 0;
  padding: 6px 12px 4px 16px !important;
  box-shadow: inset 4px 4px 10.1px 0 #000;
  background-color: #1b1c21;
}


.listAutoComletePanelBom.listAutoComletePanelBom {
  width: calc(100% - 2px);
  max-height: 672px;
  padding: 6px 3px 6px 8px;
  // margin-top: 4px;
  box-shadow: inset 4px 4px 10.1px 0 #000;
  border-radius: 0px 0px 13px 13px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  span {
    padding: 8px;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 8px;
    font-family: Inter;
    font-size: 14px;
    font-weight: 300;
    line-height: 1.4;
    letter-spacing: 0.98px;
    text-align: left;
    color: #eaecf0;
    text-transform: uppercase;

    &:hover {
      border-radius: 10px;
      background: url(../../assets/New-images/Dropdown_list_Hover.svg) no-repeat;
      background-size: cover;
      color: #0f0f14;
    }

    &[aria-selected="true"] {
      border-radius: 10px;
      background: url(../../assets/New-images/Dropdown_list_Hover.svg) no-repeat;
      color: #0f0f14;
      background-size: cover;

      &.Mui-focused {
        background: url(../../assets/New-images/Dropdown_list_Hover.svg) no-repeat;
        background-color: transparent;
        background-size: cover;
      }
    }
  }
}

.openAutoCompDropDown {
  background: url(../../assets/New-images/Mian-Dropdown-BG.svg) #1b1c21 no-repeat;
  flex-direction: column;
  background-position: right;
  border-radius: 13px 13px 0px 0px;
  background-size: cover;
  width: 100%;
  box-shadow: inset 4px 4px 10.1px 0 #000;
  position: relative;
  z-index: 11;
}

.searchString {
  font-family: Inter;
  font-size: 16px;
  font-weight: normal;
  line-height: 1.3;
  letter-spacing: -0.18px;
  text-align: left;
  color: #fff;
  padding-left: 16px;
  padding-bottom: 9px;
}

.priceQty {
  width: 95px;
  font-family: Inter;
  font-size: 16px;
  font-weight: normal;
  line-height: 1.3;
  letter-spacing: -0.18px;
  text-align: center;
  color: #fff;
  padding-bottom: 9px;
  min-height: 21px;
  display: inline-block;
}

.noDataFoundBOM {
  padding-left: 16px;
  font-family: Inter;
  font-size: 16px;
  font-weight: 200;
  font-stretch: normal;
  font-style: italic;
  line-height: 1.3;
  letter-spacing: -0.18px;
  text-align: left;
  color: #fff;
  padding-bottom: 9px;
}

.marginBottom {
  margin-bottom: 20px;
}

.selectUomBOM {
  .uomDrodownBOM {
    font-size: 18px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
    height: 45px;
    padding-left: 4px;
    border: 0;
    text-transform: uppercase;

    &:focus-visible {
      outline: none;
    }

    div:nth-child(1) {
      padding: 5px 32px 0px 0px;
    }

    .selectDropdown {
      padding: 0px;
    }

    .MuiSelect-select {
      padding: 20px 6px 6px 6px;
    }

    svg {
      transform: unset;
      color: rgba(255, 255, 255, 0.6);
      right: 10px;
    }

    fieldset {
      border: 0;
    }
  }
}

.lineStatusContainer {
  height: 214px;
}

.companyNameInput {
  width: 100%;
  height: 32px;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.04);
  padding-left: 12px;
  resize: none;
  border: none;
  color: #459fff;
  caret-color: #459fff;
  font-family: Inter;
  font-size: 15px;
  letter-spacing: 0.6px;
  text-align: left;
  outline: none;
  caret-color: #1fbbff;
  color: var(--W--01);
  transition: all 0.1s ease;

  .errorInput {
    border-color: #ff3b3b !important;

    &:focus {
      border-color: #ff3b3b !important;
    }
  }

  input {
    height: 100%;
    width: fit-content;
    background-color: transparent;
    border: 0px;
    font-family: Inter;
    font-size: 15px;
    letter-spacing: 0.6px;
    text-align: left;
    outline: none;
    caret-color: #1fbbff;
    color: #1fbbff;

    &::placeholder {
      font-family: Syncopate;
      font-size: 14px;
      letter-spacing: 0.56px;
      text-align: left;
      color: #616575;
    }

    &:focus {
      outline: none;
      color: #459fff;
    }
  }

  &:focus-within {
    outline: none;
  }
}

.autoSuggestionText {
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: 0.6px;
  text-align: left;
  color: rgba(69, 159, 255, 0.33);
  position: relative;
  top: 1px
}

.zipErrorInput.zipErrorInput {
  box-shadow: inset 2.2px 2.2px 2.2px 0 #000;
  border: 0px;
  border-image-source: linear-gradient(358deg, var(--bdr-img-clr) 253%, #2f2e33 30%);
  background-image: linear-gradient(138deg, var(--error-bg-dark) -109%, var(--error-bg-light) 87%), linear-gradient(358deg, var(--bdr-img-clr) 253%, #2f2e33 30%);
  background-origin: border-box;
  background-clip: border-box, border-box;
  z-index: 2;

  input {
    color: #fff;

    &:focus {
      color: #fff;
      box-shadow: none;
    }

    &::placeholder {
      color: white;
    }
  }
}

// MUI TextField styling to match CustomTextField



.autocompleteContainer {
  position: relative;
  width: 100%;

  :global(.MuiAutocomplete-root) {
    width: 100%;

    :global(.MuiAutocomplete-inputRoot) {
      -webkit-box-flex-wrap: nowrap;
      -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
    }

    .MuiAutocomplete-inputRoot.MuiAutocomplete-inputRoot {
      -webkit-box-flex-wrap: nowrap;
      -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
    }

    .MuiAutocomplete-noOptions.MuiAutocomplete-noOptions {
      display: none;

    }
  }
}

.muiAutocompleteTextField.muiAutocompleteTextField {
  width: 100%;
  height: 32px;

  input {
    padding: 0px !important;
  }


  :global(.MuiInputBase-root) {
    height: 32px;
    width: 100%;
    border: 1px solid transparent;
    border-radius: 8px;
    font-family: Inter;
    font-size: 15px;
    letter-spacing: 0.6px;
    color: #1fbbff;
    padding: 0 12px;
    transition: all 0.1s ease;

    &:hover {
      border-color: transparent;
    }

    &.Mui-focused {
      border-color: transparent;
      color: #1fbbff;
      box-shadow: none;
    }

    &.Mui-error {
      box-shadow: inset 2.2px 2.2px 2.2px 0 #000;
      border: 0px;
      background-image: linear-gradient(138deg, var(--error-bg-dark) -109%, var(--error-bg-light) 87%), linear-gradient(358deg, var(--bdr-img-clr) 253%, #2f2e33 30%);
      background-origin: border-box;
      background-clip: border-box, border-box;
      color: #fff;

      &.Mui-focused {
        color: #fff;
        box-shadow: none;
      }
    }
  }

  :global(.MuiInputBase-input) {
    height: 100%;
    padding: 0;
    color: inherit;
    font-family: inherit;
    font-size: inherit;
    letter-spacing: inherit;
    border: none;
    outline: none;
    background-color: transparent;
    caret-color: #1fbbff;

    &::placeholder {
      font-family: Syncopate;
      font-size: 14px;
      letter-spacing: 0.56px;
      color: #616575;
      opacity: 1;
    }

    &:focus {
      outline: none;
      color: #1fbbff;
    }

    &.Mui-error {
      color: #fff;

      &::placeholder {
        color: white;
      }
    }
  }

  :global(.MuiInputAdornment-root) {
    color: #616575;
  }

  :global(.MuiFormHelperText-root) {
    display: none;
  }

  :global(.MuiInputLabel-root) {
    display: none;
  }

  :global(.MuiOutlinedInput-notchedOutline) {
    border: none;
  }
}


.stateDropdownContainer {
  input {
    text-align: center;
  }
}

.autocompleteDropdown.autocompleteDropdown {
  max-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  gap: 8px;
  border-radius: 10px;
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  background-color: rgba(128, 130, 140, 0.28);
  margin-top: 4px;

  ul {
    &::-webkit-scrollbar {
      width: 6px;
    }

    li {
      margin-right: 3px;
      padding: 6px 16px;
      border-radius: 8px;
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: center;
      color: rgba(255, 255, 255, 0.6);
      background-color: transparent;

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
        color: #fff;
      }

      &[aria-selected='true'] {
        background-color: transparent !important;
        box-shadow: unset;
        color: #fff;

        &:hover {
          background-color: rgba(255, 255, 255, 0.2) !important;
          color: #fff;
        }
      }
    }
  }

  &.autocompleteDBA {
    ul {
      li {
        font-size: 16px;
        padding: 8px 16px;
      }
    }
  }
}

.confirmHeaderDetailsPopup {
  padding: 20px 24px;

  .dialogContent {
    width: 495px;
    height: 220px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 12px;
    background-image: linear-gradient(to bottom, #191a20, #191a20), radial-gradient(circle at 31% -2%, #fff, rgba(255, 255, 255, 0) 21%);
    background-origin: border-box;
    background-clip: content-box, border-box;
    background-color: transparent;

    .confirmHeaderDetailsContainer {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      row-gap: 40px;

      span {
        font-family: Syncopate;
        font-size: 16px;
        font-weight: bold;
        font-style: normal;
        line-height: 1.2;
        letter-spacing: 0.56px;
        text-align: center;
        color: #fff;
      }

      button {
        width: 160px;
        height: 40px;
        border-radius: 4px;
        border: solid 1px #4cff06;
        font-family: Syncopate;
        font-size: 16px;
        font-weight: normal;
        line-height: normal;
        letter-spacing: 0.52px;
        text-align: center;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background-color: #32ff6c;
          color: #0f0f14;
          border: solid 1px #32ff6c;
        }

        &:focus {
          background-color: #32ff6c;
          color: #0f0f14;
          border: solid 1px #32ff6c;
        }
      }
    }

  }
}

.uploadBOMLineTableMinHeight {
  min-height: 844px;
}