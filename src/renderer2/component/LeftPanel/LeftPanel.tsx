import { routes } from 'src/renderer2/common';
import styles from './LeftPanel.module.scss';
import ListTab from './ListTab/ListTab';
import RouteTab from './RouteTab/RouteTab';
import PdfNavigator from 'src/renderer2/pages/buyer/BomPdfExtractor/components/PdfNavigator';
import clsx from 'clsx';
import { useAppStore } from 'src/renderer2/store/AppStore';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import SellerListTab from './ListTab/SellerListTab';
import { useLocation } from 'react-router-dom';
import VideoLibraryPanel from 'src/renderer2/pages/VideoLibrary/VideoLibraryPanel';

const LeftPanel = () => {
    const location = useLocation();
    const { userData } = useGlobalStore();
    const mainWindowWidth = useAppStore(state => state.mainWindowWidth);

    if(location.pathname === routes.videoLibrary){
        return <div className={clsx(styles.leftPanel, mainWindowWidth!==null && 'flexSpace', location.pathname === routes.bomExtractor && styles.pdfNavigatorMain)}>
            <VideoLibraryPanel />
        </div>
    }

    return (
        <div className={clsx(styles.leftPanel, mainWindowWidth!==null && 'flexSpace', location.pathname === routes.bomExtractor && styles.pdfNavigatorMain)}>
            {
                (location.pathname !== routes.buyerSettingPage && location.pathname !== routes.sellerSettingPage && location.pathname !== routes.impersonateList) && <>
                    {location.pathname === routes.bomExtractor ? <div className={styles.pdfNavigatorMain}>
                        <PdfNavigator />
                    </div> : <div className={styles.listTab}>
                    {
                        (userData?.data?.type === 'BUYER' || location.pathname === routes.orderManagementPage) ? (
                            <ListTab />
                        ) : (
                            <SellerListTab />
                        )
                    }
                    </div>

                    }
                </>
            }
           
        </div>
    )
}

export default LeftPanel;
