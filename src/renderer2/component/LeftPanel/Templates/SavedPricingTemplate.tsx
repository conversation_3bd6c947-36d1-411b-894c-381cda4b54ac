import {
  commom<PERSON><PERSON>s,
  emojiRemoverRegex,
  noIdGeneric,
  priceUnits,
  useBuyerSettingStore,
  useGlobalStore,
  useSearchStore,
} from '@bryzos/giss-ui-library';
import {
  clearLocal,
  convertUtcToLocalTime,
  fetchPrice,
  formatDisplayDateForTemplate,
  formatOrderSizeToDisplayText,
  getLocal,
  getOrderSizeData,
  setLocal,
} from 'src/renderer2/helper';
import styles from '../ListTab/ListTab.module.scss';
import { ReactComponent as DeleteIcon } from '../../../assets/New-images/New-Image-latest/delete-outlined.svg';
import { ReactComponent as ShareIcon } from '../../../assets/New-images/New-Image-latest/share-outlined.svg';
import { ReactComponent as EditIcon } from '../../../assets/New-images/New-Image-latest/pencil-outlined.svg';
import { ReactComponent as ResetIcon } from '../../../assets/New-images/New-Image-latest/icon-reset.svg';
import { ReactComponent as InstantPrice } from '../../../assets/New-images/New-Image-latest/search-result/instant-pricing.svg';
import { forwardRef, useEffect, useRef, useState } from 'react';
import clsx from 'clsx';
import useDialogStore from '../../DialogPopup/DialogStore';
import useDeleteSearchProducts from 'src/renderer2/hooks/useDeleteSearchProducts';
import {
  localStorageKeys,
  routes,
  shareEmailTypes,
} from 'src/renderer2/common';
import ShareEmailWindow from '../../ShareEmailWindow/ShareEmailWindow';
import { useRightWindowStore } from 'src/renderer2/pages/RightWindow/RightWindowStore';
import usePostSaveSearchProducts from 'src/renderer2/hooks/usePostSaveSearchProducts';
import { useLocation } from 'react-router-dom';
import { useDeleteItem } from '../../../store/ItemManagerStore';
import { useRestoreDeletedItem } from 'src/renderer2/hooks/useRestoreDeletedItems';
import { backdropClasses } from '@mui/material';
import { useLeftPanelStore } from '../LeftPanelStore';
import { handleSaveSearchProducts as handleSaveSearchProductsHelper } from 'src/renderer2/helper';
interface SavedPricingTemplateProps {
  item: any;
  index: number;
  setLastClickedIndex: (index: number | null) => void;
  lastClickedIndex: number | null;
  selectedSavedSearchIdList: any[];
  setSelectedSavedSearchIdList: (ids: any[]) => void;
  isSearchMode?: boolean;
  handleSaveSearchProducts: (
    priceSearchData: any,
    saveSearchProductsMutation: any
  ) => void;
  animatedItems: Set<string>;
  handleCtrlClick: (
    item: any,
    index: number,
    onCtrlClickCallback: (currentSelectedIds: any[], updatedIds: any[]) => void
  ) => void;
}

const SavedPricingTemplate = forwardRef<
  HTMLDivElement,
  SavedPricingTemplateProps
>(
  (
    {
      item,
      index,
      setLastClickedIndex,
      lastClickedIndex,
      selectedSavedSearchIdList,
      setSelectedSavedSearchIdList,
      handleSaveSearchProducts,
      animatedItems,
      handleCtrlClick,
      isSearchMode = false,
    },
    ref
  ) => {
    const location = useLocation();
    const isBuyerDeletedItemsPage =
      location.pathname === routes.buyerDeleteOrderPage;
    const deleteItem = useDeleteItem();
    const referenceData: any = useGlobalStore((state) => state.referenceData);
    const productMapping = useGlobalStore((state) => state.productMapping);
    const setShortListedSearchProductsData = useSearchStore(
      (state) => state.setShortListedSearchProductsData
    );
    const shortListedSearchProductsData = useSearchStore(
      (state) => state.shortListedSearchProductsData
    );
    const buyerSetting = useBuyerSettingStore((state) => state.buyerSetting);
    const selectedPriceUnit = useSearchStore(
      (state) => state.selectedPriceUnit
    );
    const { selectedSavedSearch, savedSearchProducts, setSavedSearchProducts } =
      useSearchStore.getState();
    const setSelectedSavedSearch = useSearchStore(
      (state) => state.setSelectedSavedSearch
    );
    const searchZipCode = useSearchStore((state) => state.searchZipCode);
    const orderSizeSliderValue = useSearchStore(
      (state) => state.orderSizeSliderValue
    );
    const setOrderSizeSliderValue = useSearchStore(
      (state) => state.setOrderSizeSliderValue
    );
    const setSelectedPriceUnit = useSearchStore(
      (state) => state.setSelectedPriceUnit
    );
    const setSearchZipCode = useSearchStore((state) => state.setSearchZipCode);
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const { mutateAsync: deleteSearchProductsMutation } =
      useDeleteSearchProducts();
    const [orderSizeList, setOrderSizeList] = useState<any[]>([]);

    const [editingItemId, setEditingItemId] = useState<string | null>(null);
    const [editPlaceholder, setEditPlaceholder] = useState<string>('');
    const [editTitle, setEditTitle] = useState<string>('');
    const [selectedItem, setSelectedItem] = useState<any | null>(null);
    const editInputRef = useRef<HTMLInputElement>(null);
    const { setShareEmailWindowProps, setLoadComponent, setShareEmailType } =
      useRightWindowStore();
    const setSelectedProductsData = useSearchStore(
      (state) => state.setSelectedProductsData
    );
    const filterShortListedSearchProductsData = useSearchStore(
      (state) => state.filterShortListedSearchProductsData
    );
    const setFilterShortListedSearchProductsData = useSearchStore(
      (state) => state.setFilterShortListedSearchProductsData
    );
    const setSaveFeedbackMap = useSearchStore(
      (state) => state.setSaveFeedbackMap
    );
    const setFocusSingleProduct = useSearchStore(
      (state) => state.setFocusSingleProduct
    );
    const { mutateAsync: saveSearchProductsMutation } =
      usePostSaveSearchProducts();
    const { restoredItem, handleRestoreDeletedItem, isLoading } =
      useRestoreDeletedItem();
    const setClickedCreateNewButton = useLeftPanelStore(state => state.setClickedCreateNewButton);
    useEffect(() => {
      if (referenceData?.ref_weight_price_brackets?.length > 0) {
        setOrderSizeList(referenceData.ref_weight_price_brackets);
      }
    }, [referenceData]);

    useEffect(() => {
      if (item)
        if (item?.id?.includes(noIdGeneric) && !animatedItems.has(item.id)) {
          setTimeout(() => {
            animatedItems.add(item.id);
          }, 3000);
        }
    }, [item]);

    const handleItemClick = async (
      item: any,
      index: number,
      event: React.MouseEvent | undefined,
      isEdit?: boolean
    ) => {
      try {
        // Handle shift-click for multiple selection
        if (event?.shiftKey && lastClickedIndex !== null) {
          handleCtrlClick(item, index, handleWhenCtrlClickOnItem);
          return;
        }

        // // Handle Ctrl/Cmd+click for individual selection
        // if (event.ctrlKey || event.metaKey) {
        //     handleCtrlClick(item, index);
        //     return;
        // }

        // Regular click behavior
        if (item.id === selectedSavedSearch?.id) {
          return;
        }
        setLastClickedIndex(index);
        setShareEmailWindowProps(null);
        setShareEmailType(null);
        setLoadComponent(null);
        setSelectedSavedSearchIdList([item.id]); // Reset to single selection
        populatePriceSearchData(item);
      } catch (error) {
        console.error('Error saving pricing:', error);
      }
    };
    const populatePriceSearchData = (item: any) => {
      if (
        selectedSavedSearch?.title &&
        getLocal(localStorageKeys.instantPriceSearch, null)
      ) {
        handleSaveSearchProducts(
          selectedSavedSearch,
          saveSearchProductsMutation
        );
      }
      setSelectedSavedSearch(item);
      const brackets = referenceData?.ref_weight_price_brackets || [];
      let orderSize = Number(item.order_size);

      // Find the appropriate bracket based on order size
      const matchingBracket = getOrderSizeData(brackets, orderSize);

      if (matchingBracket) {
        orderSize = matchingBracket.min_weight;
      }
      setOrderSizeSliderValue(Number(orderSize));
      setSearchZipCode(item.zipcode);
      setSelectedPriceUnit(
        item.products?.[0]?.price_unit?.toLowerCase() === priceUnits.ea
          ? priceUnits.pc
          : item.products?.[0]?.price_unit?.toLowerCase() || priceUnits.cwt
      );
      const setShortListedSearchProductsData =
        useSearchStore.getState().setShortListedSearchProductsData;
      const shortListedSearchProductsData =
        useSearchStore.getState().shortListedSearchProductsData;
      setShortListedSearchProductsData([]);
      if (item.products?.length > 0) {
        fetchPrice(
          item.products,
          item.zipcode,
          parseFloat(item.order_size.replace(/[$,]/g, ''))
        );
      }
    };

    // const handleShiftClick = (currentIndex: number) => {
    //     if (lastClickedIndex === null) return;

    //     const startIndex = Math.min(lastClickedIndex, currentIndex);
    //     const endIndex = Math.max(lastClickedIndex, currentIndex);

    //     const selectedIds = items
    //         .slice(startIndex, endIndex + 1)
    //         .map((item: any) => item.id);

    //     setSelectedSavedSearchIdList(selectedIds);
    //     setLastClickedIndex(currentIndex);

    //     // Console log the selected items for debugging
    //     console.log('Shift-click selection:', {
    //         startIndex,
    //         endIndex,
    //         selectedIds,
    //         selectedItems: items.slice(startIndex, endIndex + 1)
    //     });
    // };

    // const handleCtrlClick = (item: any, index: number, onCtrlClickCallback: (currentSelectedIds: any[], updatedIds: any[]) => void) => {
    //     const currentSelectedIds = [...selectedSavedSearchIdList];
    //     const itemId = item.id;
    //     let updatedIds: any[] = [];

    //     if (currentSelectedIds.includes(itemId)) {
    //         // Remove item from selection
    //         updatedIds = currentSelectedIds.filter(id => id !== itemId);
    //         setSelectedSavedSearchIdList(updatedIds);
    //     } else {
    //         // Add item to selection
    //         updatedIds = [...currentSelectedIds, itemId];
    //         setSelectedSavedSearchIdList(updatedIds);
    //     }
    //     onCtrlClickCallback(currentSelectedIds, updatedIds);
    //     setLastClickedIndex(index);
    // };

    const handleWhenCtrlClickOnItem = (
      currentSelectedIds: any[],
      updatedIds: any[]
    ) => {
      const itemId = item.id;
      if (currentSelectedIds.includes(itemId)) {
        if (selectedSavedSearch?.id === itemId) {
          clearSelectedSavedSearch();
          const itemIndex = savedSearchProducts.findIndex(
            (item: any) => item.id === updatedIds[0]
          );
          if (savedSearchProducts[itemIndex]) {
            populatePriceSearchData(savedSearchProducts[itemIndex]);
          }
        }
      }
      if (
        updatedIds.length === 1 &&
        selectedSavedSearch?.id !== updatedIds[0]
      ) {
        const itemIndex = savedSearchProducts.findIndex(
          (item: any) => item.id === updatedIds[0]
        );
        handleItemClick(savedSearchProducts[itemIndex], itemIndex, undefined);
        return;
      }
    };

    const handleEditTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      e.stopPropagation();
      e.target.value = e.target.value.replace(emojiRemoverRegex, '');
      setEditTitle(e.target.value);
    };

    // Handle keypress in edit mode
    const handleKeyDown = (e: React.KeyboardEvent, item: any) => {
      e.stopPropagation();
      if (e.key === 'Enter') {
        handleSaveEdit(e, item);
      } else if (e.key === 'Escape') {
        handleCancelEdit(e);
      }
    };

    const handleSaveEdit = (e: any, data: any) => {
      e.stopPropagation();
      let updatedItem = {};
      if (data.title === editTitle) {
        setEditingItemId(null);
        setEditTitle('');
        return;
      }
      // Update the history items with the new title
      const updatedItems = savedSearchProducts.map((item) => {
        if (item.id === editingItemId) {
          item = { ...item, title: editTitle.trim() || 'Untitled' };
          updatedItem = item;
        }
        return item;
      });
      setSavedSearchProducts(updatedItems);
      setSelectedSavedSearch(updatedItem);
      setLocal(localStorageKeys.instantPriceSearch, updatedItem);
      setEditingItemId(null);
      setEditTitle('');
    };

    const handleCancelEdit = (e: any) => {
      e.stopPropagation();
      setEditingItemId(null);
      setEditTitle('');
    };

    const handleEditClick = (e: React.MouseEvent, item: any) => {
      e.stopPropagation(); // Prevent triggering the item click
      setEditingItemId(item.id);
      setEditPlaceholder(item.title);
      setEditTitle('');
      // Find the index of the item
      handleItemClick(item, index, e, true);
    };

    const handleDeleteClick = async (e: React.MouseEvent, item: any) => {
      e.stopPropagation(); // Prevent triggering the item click
      if (item?.id.includes(noIdGeneric)) {
        const pricingId = await saveStoreLocalPriceSearchData();
        if(pricingId){
          item.id = pricingId;
        }

      }
      try {
        const payload = {
          data: [item.id],
        };
        await deleteSearchProductsMutation(payload);
        setSelectedSavedSearch(null);
        setShortListedSearchProductsData([]);
        setSelectedSavedSearchIdList([]);
        setLastClickedIndex(null);
        deleteItem(item, 'INSTANT_PRICING');
        setClickedCreateNewButton(Math.random());
        // setFilterShortListedSearchProductsData([]);
        // setSelectedProductsData([]);
        // setSaveFeedbackMap({});
        // setFocusSingleProduct({});
      } catch (error) {
        console.error('Failed to delete item:', error);
        showCommonDialog(
          null,
          commomKeys.errorContent,
          commomKeys.actionStatus.error,
          resetDialogStore,
          [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
        );

        // You might want to show an error toast here
      }
    };

    const handleShareClick = (e: React.MouseEvent, item: any) => {
      e.stopPropagation();
      if(item?.pricing_expired){
        return;
      }
      setShareEmailWindowProps({ isSharePrice: true });
      setLoadComponent(<ShareEmailWindow />);
      setShareEmailType(shareEmailTypes.sharePrice);
      handleItemClick(item, index, e, true);
    };

    const clearSelectedSavedSearch = () => {
      setSelectedSavedSearch(null);
      setSelectedProductsData([]);
      setSaveFeedbackMap({});
      setFocusSingleProduct({});
      setFilterShortListedSearchProductsData([]);
    };
    let animate = false;
    if (item?.id?.includes(noIdGeneric) && !animatedItems.has(item.id)) {
      animate = true;
    }

    const saveStoreLocalPriceSearchData = async () => {
      const localPriceSearchData = getLocal(localStorageKeys.instantPriceSearch, null);
      if (localPriceSearchData && typeof localPriceSearchData === 'object' && 'title' in localPriceSearchData) {
       const pricingId = await handleSaveSearchProductsHelper(localPriceSearchData, saveSearchProductsMutation);
       return pricingId;
      }
  }

    return (
      <div
        ref={ref}
        className={clsx(
          styles.searchItemContainer,
          (selectedSavedSearch?.id === item.id ||
            selectedSavedSearchIdList.includes(item.id)) &&
            styles.selectedSearchItem
        )}
        key={item.id}
        onClick={(e) => {
          handleItemClick(item, index, e);
        }}
      >
        <div className={styles.searchTitle}>
          {editingItemId === item.id && (
            <>
              <div className={styles.editTitleRow}>
                <input
                  ref={editInputRef}
                  type='text'
                  className={styles.editTitleInput}
                  placeholder={editPlaceholder}
                  value={editTitle}
                  onChange={handleEditTitleChange}
                  onKeyDown={(e) => handleKeyDown(e, item)}
                  onBlur={(e) => handleSaveEdit(e, item)}
                  onClick={(e) => e.stopPropagation()}
                  maxLength={50}
                  autoFocus
                />
                {/* <div className={styles.editButtons}>
                                            <button
                                                className={styles.editButton}
                                                onClick={handleCancelEdit}
                                            >
                                                Cancel
                                            </button>
                                            <button
                                                className={`${styles.editButton} ${styles.saveButton}`}
                                                onClick={handleSaveEdit}
                                            >
                                                Save
                                            </button>
                                        </div> */}
              </div>
            </>
          )}
          <>
            <span
              className={clsx(styles.searchTitleText, {
                [styles.initialPositionForAnimation]: animate,
                [styles.slideInAnimation1]: animate,
              })}
            >
              <span className={styles.searchTitleTextSpan}>{item.title}</span>
              {!isBuyerDeletedItemsPage && (
                <span
                  className={styles.editIcon}
                  onClick={(e) => handleEditClick(e, item)}
                >
                  <EditIcon />
                </span>
              )}
            </span>
          </>
          <span className={styles.itemCount}>
            {isSearchMode&&<span><InstantPrice /></span>}{item.item_count} {item.item_count <= 1 ? 'Item' : 'Items'}
          </span>
          <div className={styles.iconContainer}>
            {!isBuyerDeletedItemsPage ? (
              <>
                <span
                  className={clsx(styles.shareIcon, item?.pricing_expired && styles.pricingExpired)}
                  onClick={(e) => handleShareClick(e, item)}
                >
                  <ShareIcon />
                </span>
                <span
                  className={styles.deleteIcon}
                  onClick={(e) => handleDeleteClick(e, item)}
                >
                  <DeleteIcon />
                </span>
              </>
            ) : (
              <span
                className={styles.deleteIcon}
                onClick={(e) => {
                  e.stopPropagation();
                  handleRestoreDeletedItem(item)
                }}
              >
                <ResetIcon />
              </span>
            )}
          </div>
        </div>
        <div className={styles.searchDetails}>
          <div className={styles.positionRelative}>
            &nbsp;
            <span
              className={clsx({
                [styles.initialPositionForAnimation]: animate,
                [styles.slideInAnimation2]: animate,
              })}
            >
              {item?.products?.length > 0
                ? Array.from(
                    new Set(
                      item?.products.map(
                        (obj: any) => productMapping[obj.product_id]?.Key2 ?? ''
                      )
                    )
                  ).join(', ')
                : '-'}
            </span>
          </div>
          <div className={styles.positionRelative}>
            &nbsp;
            <span
              className={clsx({
                [styles.initialPositionForAnimation]: animate,
                [styles.slideInAnimation3]: animate,
              })}
            >
              {formatOrderSizeToDisplayText(
                orderSizeList,
                Number(item.order_size)
              )}
            </span>
          </div>
          <div className={styles.positionRelative}>
            &nbsp;
            <span
              className={clsx({
                [styles.initialPositionForAnimation]: animate,
                [styles.slideInAnimation4]: animate,
              })}
            >
              {item?.created_date ? formatDisplayDateForTemplate(convertUtcToLocalTime(item?.created_date)) : '-'}
            </span>
          </div>
        </div>
      </div>
    );
  }
);
export default SavedPricingTemplate;
