import React, { forwardRef, useEffect, useState } from 'react';
import styles from './AppUpdateNotification.module.scss';
import { ReactComponent as <PERSON><PERSON><PERSON><PERSON><PERSON>ogo } from '../../assets/New-images/Logo.svg';
import { useHeightListener } from 'src/renderer2/hooks/useHeightListener';

interface AppUpdateNotificationProps {
  updateUrl?: string;
  channelWindow: any;
  currentVersion: string;
  releaseVersion: string;
}

const AppUpdateNotification = forwardRef<HTMLDivElement, AppUpdateNotificationProps>(({
  updateUrl,
  channelWindow,
  currentVersion,
  releaseVersion,
},ref) => {
  const containerRef = useHeightListener(currentVersion);

  useEffect(() => {
    if(window.electron.handleZoom) window.electron.handleZoom();

    }, []);

  const handleUpdateClick = () => {
    if (updateUrl) {
      window.open(updateUrl, '_blank');
    }
  };
  const onClose = () => {
    if (channelWindow?.close) {
      window.electron.send({ channel: channelWindow.close })
    }
  }
  const onMinimize = () => {
    if (channelWindow?.minimize) {
      window.electron.send({ channel: channelWindow.minimize })
    }
  }

  return (
    <div ref={containerRef} className={`${styles.container}`} style={{height:'800px' , width: '600px'}}>
    <div className={styles.modal}>
      {/* Close Button */}
        <div className={styles.header}>
          <div className={styles.dragPanel}>

          </div>
          <div className={styles.windowControls}>
            {onMinimize && (
              <button
                className={styles.minimizeBtn}
                onClick={onMinimize}
                type="button"
                title="Minimize"
              >
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                  <path d="M2 6H10" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                </svg>
              </button>
            )}
            {onClose && (
              <button
                className={styles.closeBtn}
                onClick={onClose}
                type="button"
                title="Close"
              >
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                  <path d="M9 3L3 9M3 3L9 9" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                </svg>
              </button>
            )}
          </div>
        </div>


      {/* Title */}
      <h2 className={styles.title}>NEW UPDATE AVAILABLE</h2>

      {/* Version Information */}
      <div className={styles.versionInfo}>
        <p>New version {releaseVersion} is now available.</p>
        <p>You are currently using {currentVersion}.</p>
      </div>

      {/* Update Contents */}
      <div className={styles.updateContents}>
        <div>This update includes:</div>
          <div
            className="releaseNotesContent"
          >
            <ul>
              <li>New user interface</li>
              <li>Regional & Volume-based pricing</li>
              <li>Ability to upload Bills of Material (PDF)</li>
              <li>Ability to create quotes</li>
              <li>Ability to create draft POs</li>
              <li>Ability to view saved activity</li>
              <li>Save multiple shipping addresses</li>
              <li>View and set notification preferences</li>
              <li>View instructional videos for tooltip guidance</li>
            </ul>
          </div>
        </div>
      {/* Action Buttons */}
      <div className={styles.actionButtons}>
        <div className={styles.updateAppButton} >
          <p>UPDATING...</p>
          <p className={styles.updateAppButtonText}>(Please wait while we update the application.)</p>
        </div>
      </div>
      <p className={styles.downloadText}>
        or  <button onClick={handleUpdateClick} className={styles.downloadButton}> click here to download</button>
      </p>
    </div>
    </div>
  );
});

AppUpdateNotification.displayName = 'AppUpdateNotification';

export default AppUpdateNotification; 