.toggleContainer {
  display: flex;
  align-items: center;
  gap: 10px;
}

.toggle {
  position: relative;
  width: 58px;
  height: 26px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 60px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  transition: background-color 0.3s ease;
  padding: 0px 9px;
  box-sizing: border-box;

  &:focus {
    outline:1px solid #71737f;
  }

  &.active {
    background-image: linear-gradient(118deg, #f9f9f9 16%, #e4e4e6 77%);
    justify-content: flex-start;

    .toggleSwitch {
      transform: translateX(34px);
      background-color: #0f0f14;
    }

    .toggleText {
      font-family: Inter;
      font-size: 12px;
      font-weight: 600;
      font-stretch: normal;
      font-style: normal;
      line-height:1;
      letter-spacing: normal;
      text-align: left;
      color: #0f0f14;
    }
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;

    .toggleSwitch {
      background-color: #9b9eac;
    }

    .toggleText {
      color: #9b9eac;
    }
  }

  .toggleSwitch {
    position: absolute;
    left: 5px;
    width: 16.8px;
    height: 16.8px;
     background-color: #9b9eac;
    border-radius: 50%;
    transition: transform 0.3s ease;
  }

  .toggleText {
    font-size: 12px;
    font-weight: bold;
    color: #fff;
    z-index: 1;
    text-align: center;
    user-select: none;
  }
}

.toggleLabel {
  font-size: 14px;
}