
.blurredContainer {
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    position: absolute;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 21px;
}
.claimOrderRightWindow {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 16px;
    width: 100%;
    background-color: #191a20;
    // height: 100vh;
  }

  .topSection {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .chatSection {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-top: 16px;
  }
  
  .summaryCard {
    font-family: Inter;
    font-size: 18px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
    display: flex;
    flex-direction: column;
  }
  
  .summaryRow {
    display: flex;
    justify-content: space-between;
    line-height: 1.2;
    align-items: end;
  }
  .summaryRowSalesTax {
    opacity: 0.4;
  }
  .summaryCardContent {
      box-shadow: inset -2.1px -2px 4.1px 0 #000;
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
      background-color: rgba(217, 217, 217, 0.04);
      // height: 62px;
      padding: 10px;
      display: flex;
      flex-flow: column;
      gap: 6px;
  }
  .summaryCardContentOrderManagement {
    height: 100%;
    gap:10px;
  }
  .summaryTotal {
    display: flex;
    justify-content: space-between;
    background-image: linear-gradient(354deg, #000 142%, #191a20 4%);
    padding: 10px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
  }
  .summaryTotalOrderManagement {
    line-height: 1.2;
    align-items: center;
  }
  .infoText {
    font-family: Inter;
    font-size: 12px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.6;
    letter-spacing: -0.36px;
    text-align: center;
    color: #9b9eac;
    padding: 0 24px;
  }
  
  .acceptButtonDummy {
    border-radius: 10px;
    background-color: #222329;
    font-family: Syncopate;
    font-size: 18px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: -0.72px;
    text-align: center;
    color: #71737f;
    padding: 14px 0px;
    cursor: not-allowed;
  }
  .acceptButton {
    width: 100%;
    border-radius: 10px;
    box-shadow: 0 -2px 8px 0 rgba(0, 0, 0, 0.8);
    background-image: linear-gradient(128deg, #1c40e7 -23%, #16b9ff 116%);
    color: #fff;
    font-family: Syncopate;
    font-size: 18px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: -0.72px;
    text-align: center;
    padding: 14px 0px;
    &:hover {
        background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(128deg, #1c40e7 -23%, #16b9ff 116%);
    }
  }
  .availableToClaim {
      font-family: 'Syncopate', sans-serif;
      font-size: 12px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.2;
      letter-spacing: 0.48px;
      text-align: center;
      color: #32ccff;
      text-transform: uppercase;
      padding-bottom: 20px;
  }
  .undeleteButton {
    border-radius: 10px;
    background-color: #222329;
    font-family: Syncopate;
    font-size: 18px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: -0.72px;
    text-align: center;
    color: #fff;
    padding: 14px 0px;
    width: 100%;
    &:hover {
        background-color: #33343a;
    }
  }