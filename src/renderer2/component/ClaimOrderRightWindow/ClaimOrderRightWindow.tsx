import React, { useEffect, useMemo } from 'react'
import styles from './ClaimOrderRightWindow.module.scss'
import { useRightWindowStore } from 'src/renderer2/pages/RightWindow/RightWindowStore';
import { commomKeys, format4DigitAmount, formatToTwoDecimalPlaces, getChannelWindow, priceUnits, purchaseOrder, useChatWithVendorStore, useGlobalStore, useOrderManagementStore } from '@bryzos/giss-ui-library';
import { useLocation, useNavigate } from 'react-router-dom';
import { routes } from 'src/renderer2/common';
import PdfMakePage from 'src/renderer2/pages/PdfMake/pdfMake';
import { descriptionLines, getOtherDescriptionLines } from 'src/renderer2/utility/pdfUtils';
import clsx from 'clsx';
import ChatWithVendor from '../ChatWithVendor/ChatWithVendor';
import usePostDisputeOrder from 'src/renderer2/hooks/usePostDisputeOrder';
import useDialogStore from '../DialogPopup/DialogStore';

const ClaimOrderRightWindow = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const channelWindow = getChannelWindow();
    const { props } = useRightWindowStore();
    const { isRequiredSellerSettingsFilled } = useGlobalStore();
    const {
        orderDetail, availableTime, totalOrderValue, disableNextButton, disableOnclick, dialogRef, openSubmitApp,
        handleClickOpen, nextPage, setOpenReminderYouAreAlmostTherePopup, setDisableOnclick, setIsReminderPopup, handleUnDeleteClick, componentType,
        finalDisputePayload
    } = props || {};
    const { setPoNumber, setChannelName, setCompanyName, poNumber } = useChatWithVendorStore();
    const { mutateAsync: disputeOrderMutation } = usePostDisputeOrder();
    const { showCommonDialog, resetDialogStore }: any = useDialogStore();
    const setShowLoader = useGlobalStore(state => state.setShowLoader);

    const isOrderManagementPage = componentType === 'ORDER' || location.pathname === routes.orderManagementPage;
    const isPreviewOrderPage = location.pathname === routes.previewOrderPage;
    const isDeleteOrderPage = location.pathname === routes.deleteOrderPage;
    const orderManageMentInitialData= useOrderManagementStore(state => state.orderManageMentInitialData);

    useEffect(() => {
        if (isOrderManagementPage && orderDetail?.seller_po_number !== poNumber) {
            const channelName = 'S' + orderDetail?.seller_po_number.substring(1, orderDetail?.seller_po_number?.length);
            setPoNumber(orderDetail?.buyer_internal_po)
            setChannelName(channelName)
            setCompanyName(orderDetail?.buyer_company_name ? orderDetail.buyer_company_name : 'Buyer Company Name')
        }
    }, [orderDetail])


    const handleExportPDfClick = ($event) => {
        $event.stopPropagation();
        if (isRequiredSellerSettingsFilled) {
            setOpenReminderYouAreAlmostTherePopup(false);
            setDisableOnclick(false)
        } else {
            setIsReminderPopup(false);
            setDisableOnclick(true);
            setOpenReminderYouAreAlmostTherePopup(true);
        }
    }

    const calculateSellerLineWeight = (data) => {
        return formatToTwoDecimalPlaces(data.total_weight)
    }

    const getCartItems = () => {
        const { items } = orderDetail;
        const formattedItems = items.map((item, index) => ({
            description: descriptionLines(item.description),
            otherDescription: getOtherDescriptionLines(item.description),
            product_tag: item.product_tag,
            domesticMaterialOnly: item.domestic_material_only ? '\nDomestic (USA) Material Only' : '',
            qty: formatToTwoDecimalPlaces(item.qty),
            qty_unit: item.qty_unit,
            price_unit: item.price_unit,
            price: item.price_unit.toLowerCase() === priceUnits.lb ? format4DigitAmount(item.seller_price_per_unit) : formatToTwoDecimalPlaces(item.seller_price_per_unit),
            line_weight: calculateSellerLineWeight(item),
            extended: formatToTwoDecimalPlaces(item.seller_line_total),
            line_weight_unit: "Lb",
            line_no: index,
            po_line: index.toString(),
            total_weight: item.total_weight
        }));
        return formattedItems
    }

    const handleSubmitReply = async () => {
        try{
            setShowLoader(true);
            resetDialogStore();
            console.log("handleSubmitReply finalDisputePayload @>>>>>>>", finalDisputePayload);
            const payload = {
                "data": {...finalDisputePayload}
            };
            const response = await disputeOrderMutation(payload);
            console.log("handleSubmitReply response @>>>>>>>", response);
            if(response?.data?.error_message){
                showCommonDialog(null, response?.data?.error_message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
                return;
            }
        }catch(error){
            console.log("handleSubmitReply error @>>>>>>>", error);
            showCommonDialog(null, commomKeys.errorContent, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
            return;
        }finally{
            setShowLoader(false);
        }
    }

    const handleSubmitBtnClick = () => {
        handleSubmitReply();
    }

    const initialOrder = orderManageMentInitialData?.cart_items?.filter(
        (item: any) => (!item?.event || item?.event !== "add_line" ) && !item?.line_cancel_date
      ).map((item: any) => item?.po_line);

    
    function areAllLinesCancelled(initialOrder : any, finalPayload : any) {
        if(!initialOrder || !finalPayload) return false;
        return initialOrder?.every((poLine : any) =>
          finalPayload?.some((item : any) =>
            item?.po_line === poLine &&
            item?.action === 'accept' &&
            item?.qty === 0
          )
        );
    }

    const repliesCount = useMemo(() => {
        if (!finalDisputePayload) return 0;
        
        let count = 0;
        if (finalDisputePayload?.order_level && typeof finalDisputePayload?.order_level === 'object') {
            count += Object.keys(finalDisputePayload?.order_level).length;
        }
        if (finalDisputePayload?.order_line_level && Array.isArray(finalDisputePayload?.order_line_level)) {
            count += finalDisputePayload?.order_line_level.length;
        }
        return count;
    }, [finalDisputePayload]);
      
    return (<>
        <div className={styles.claimOrderRightWindow}>
            {/* <div className={clsx(openSubmitApp && styles.blurredContainer)}></div>   */}
            <div className={styles.topSection}>
                <div className={styles.summaryCard}>
                <div className={clsx(styles.summaryCardContent, isOrderManagementPage && styles.summaryCardContentOrderManagement)}>
                    <div className={styles.summaryRow}>
                        <div>{(isOrderManagementPage && Boolean(orderDetail?.order_level_dispute)) ? <span> Original  <br/>Material Total </span> : "Material Total"}</div>
                        <div>$ {formatToTwoDecimalPlaces(orderDetail?.seller_po_price)}</div>
                    </div>
                    {isOrderManagementPage && Boolean(orderDetail?.order_level_dispute) && (
                        <div className={styles.summaryRow}>
                            <div>Pending <br/>Change Orders</div>
                            <div>($ {formatToTwoDecimalPlaces(orderDetail?.order_level_dispute?.pending_amount || "0")})</div>
                        </div>
                    )}
                    {(isOrderManagementPage && Boolean(orderDetail?.order_level_dispute)) && (
                        <div className={styles.summaryRow}>
                            <div>Restocking Fee</div>
                            <div>$ {formatToTwoDecimalPlaces(orderDetail?.order_level_dispute?.restocking_fee || "0")}</div>
                        </div>
                    )}
                    <div className={clsx(styles.summaryRow, styles.summaryRowSalesTax)}>
                        <div>Sales Tax</div>
                        <div>{parseFloat(orderDetail?.seller_sales_tax) > 0 ? `$ ${formatToTwoDecimalPlaces(orderDetail?.seller_sales_tax)}` : 'Exempt'}</div>
                    </div>
                </div>
                <div className={clsx(styles.summaryTotal, isOrderManagementPage && styles.summaryTotalOrderManagement)}>
                    <div>{(isOrderManagementPage && Boolean(orderDetail?.order_level_dispute)) ? <span> Pending <br/>Total Sale </span> : "Total Sale"}</div>
                    <div>$ {formatToTwoDecimalPlaces((isOrderManagementPage && Boolean(orderDetail?.order_level_dispute)) ? orderDetail?.order_level_dispute?.total_pending_amount : totalOrderValue)}</div>
                </div>
            </div>
            <div className={styles.infoText}>
                { isOrderManagementPage ?
                    "Total Sale Amount includes cost of material and delivery."
                    :
                    "After clicking “Accept Order,” the next screen will be your order confirmation. Additionally, we will send you a purchase order for your records."
                }
            </div>
                    <div>
                    {orderDetail?.claimed_by === purchaseOrder.pending  && (isPreviewOrderPage || isDeleteOrderPage) && <div className={styles.availableToClaim}>AVAILABLE TO CLAIM @ {availableTime}</div>}
                    {
                        ( !isPreviewOrderPage && !isOrderManagementPage) && 
                      <>
                    {(orderDetail?.is_order_hidden === true || isDeleteOrderPage) ?
                        <button className={styles.undeleteButton} onClick={handleUnDeleteClick}>
                            UNDELETE
                        </button>
                        :
                        <button className={styles.acceptButton} onClick={handleClickOpen}>
                            ACCEPT ORDER
                        </button>
                    }
                      </>
                    }
                    {(finalDisputePayload && (finalDisputePayload?.order_line_level?.length > 0 || Object.keys(finalDisputePayload?.order_level || {})?.length > 0)) && (
                        <div>
                            <button className={styles.acceptButton} onClick={handleSubmitBtnClick} disabled={false}>
                                SUBMIT {repliesCount > 0 ? `${repliesCount} ${repliesCount > 1 ? 'REPLIES' : 'REPLY'}` : 'REPLY'}
                            </button>
                        </div>
                    )}

                </div>
            </div>

            
        </div>

        {
                ((location.pathname === routes.orderManagementPage || location.pathname === routes.searchResult) && orderDetail && orderDetail?.buyer_id && orderDetail?.cancel_date === null) &&
                    <ChatWithVendor
                        close={() => { }}
                        userName={orderDetail?.buyer_name || 'Buyer Name'}
                    />
            }
      </>

    )
}

export default ClaimOrderRightWindow