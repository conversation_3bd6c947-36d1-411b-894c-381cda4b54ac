.notification-container {
    width: 100%;
}

.notification-loader {
    padding: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.notification-empty {
    padding: 16px;
    font-size: 14px;
    color: #444;
}

.notification-error {
    padding: 8px 16px;
    font-size: 12px;
    color: red;
}

.notification-section {
    display: flex;
    flex-direction: column;
    gap: 2px;
    margin-bottom: 12px;
    .notification-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        padding: 8px 8px 10px;
        border-radius: 10px;
        background-color: #fff;
        cursor: pointer;
        font-family: Inter;
        font-size: 12px;
        font-weight: 300;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: 0.48px;
        text-align: left;
        color: #0f0f14;
        position: relative;
        height: 60px;
        width: 100%;

        .notification-content {
            flex: 1;
            display: flex;
            width: 100%;


            .title {
                font-family: Inter;
                font-size: 12px;
                font-weight: bold;
                font-stretch: normal;
                font-style: normal;
                line-height: 1;
                letter-spacing: 0.7px;
                text-align: right;
                color: #0f0f14;
            }

           
        }
        .notification-header {
            display: flex;
            justify-content: right;
            align-items: center;
            width: 100%;

            .time {
                font-family: Inter;
                font-size: 10px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1;
                letter-spacing: 0.7px;
                text-align: right;
                color: #71737f;
            }
        }

        .clear-btn {
            cursor: pointer;
            display: flex;
            align-items: center;


            svg {
                font-size: 16px;
                color: #9B9EAC;

                &:hover {
                    color: #000;
                }
            }
        }
    }
}

.clear-all {
    padding-bottom: 12px;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    button {
        height: 28px;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        padding: 8px 15px;
        border-radius: 500px;
        background-color: rgba(255, 255, 255, 0.04);
        font-family: Inter;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: normal;
        text-align: left;
        color: #71737f;
        &:hover{
            background-image: linear-gradient(104deg, #1c40e7 -1%, #16b9ff 102%);
            background-color: transparent;
            color: #fff;
        }
    }
}
.message {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    width: calc(100% - 15px);
}
.dot {
    background-color: #16b9ff;
    height: 8px;
    width: 8px;
    border-radius: 50%;
}
.dotUnread {
    background-color: #222329;
}
.notification-item-container {
    display: flex;
    align-items: center;
    gap: 4px
}