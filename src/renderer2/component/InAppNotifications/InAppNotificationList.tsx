import React, { useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import CloseIcon from "@mui/icons-material/Close";
import {
  useInAppNotificationStore,
  formatRelativeLabel,
} from "./InAppNotificationStore";
import usePostClearInAppNotifications from "src/renderer2/hooks/usePostClearInAppNotifications";
import usePostMarkReadInAppNotifications from "src/renderer2/hooks/usePostMarkReadInAppNotifications";
import "./InAppNotificationList.scss";
import { createActionUrl, handleOrderManagementNavigation } from "src/renderer2/helper";

type Props = {
  onClose?: () => void;
};

export default function InAppNotificationList({ onClose }: Props) {
  const navigate = useNavigate();
  const location = useLocation();
  const {
    loading,
    error,
    getUnreadList,
    getReadList,
    clearAll,
    clearByIds,
    onPanelOpen,
    order,
    setNavigationURL,
    onPanelClose,
  } = useInAppNotificationStore((s) => ({
    loading: s.loading,
    setNavigationURL: s.setNavigationURL,
    error: s.error,
    unreadList: s.unreadList,
    readList: s.readList,
    unreadCount: s.unreadCount,
    getUnreadList: s.getUnreadList,
    getReadList: s.getReadList,
    clearAll: s.clearAll,
    clearByIds: s.clearByIds,
    onPanelOpen: s.onPanelOpen,
    onPanelClose: s.onPanelClose,
    order: s.order,
  }));

  const { mutateAsync: clearHook } = usePostClearInAppNotifications();
  const { mutateAsync: markHook } = usePostMarkReadInAppNotifications();

  const unreadList = getUnreadList();
  const readList = getReadList();

  useEffect(() => {
    onPanelOpen();
    
    return () => {
      const ids = onPanelClose();
      if (ids.length > 0) {
        markHook(ids).catch((e) => console.error(e));
      }
    };
  }, []);

  const handleNotificationClick = (id: string, route: string | null) => {
    if(route){
      const url = createActionUrl(route, id);
      setNavigationURL(url);      
    }
  };

  const handleRowClear = async (id: string) => {
    try {
      await clearHook([id]);
      clearByIds([id]);
    } catch (e) {
      console.error(e);
    }
  };

  const clearAllNotifications = async () => {
    try {
      await clearHook(readList.map((n: any) => n.id));
      clearByIds(readList.map((n: any) => n.id));
    } catch (e) {
      console.error(e);
    }
  };

  if (loading) {
    return (
      <div className="notification-loader">
        <div className="loader">Loading...</div>
      </div>
    );
  }

  if (!unreadList.length && !readList.length) {
    return (
      <div className="notification-empty">
        <p>You’re all caught up.</p>
      </div>
    );
  }

  return (
    <div className="notification-container">
      {error && <div className="notification-error">{error}</div>}

      {/* Unread */}
      {unreadList.length > 0 && (
        <div className="notification-section">
          {unreadList.map((n) => {
            const handleRowClick = (target: string) => {
              handleNotificationClick(n.id, n.notification_route)
            }
            return (
            <div className="notification-item-container" key={n.id}>
              <div className="dot"></div>
              <div className="notification-item unread">
                
                <div className="notification-header">
                    <span className="time">
                      {formatRelativeLabel(n.sentEpoch)}
                    </span>
                  </div>
                <div className="notification-content" onClick={() => handleOrderManagementNavigation(n.notification_route, location.pathname, handleRowClick)}>
                  <p className="message"><span className="title">{n.notification_title}</span> {n.message}</p>
                  <div className="clear-btn" onClick={() => handleRowClear(n.id)}>
                    <CloseIcon />
                  </div>
                </div>
              </div>
            </div>
          )})}
        </div>
      )}

      {/* Clear All */}
      {readList.length > 0 && (
        <div className="clear-all">
          <button onClick={clearAllNotifications}>Clear All</button>
        </div>
      )}

      {/* Read */}
      {readList.length > 0 && (
        <div className="notification-section read">
          {readList.map((n) => {
            const handleRowClick = (target: string) => {
              handleNotificationClick(n.id, n.notification_route)
            }
            return (
            <div className="notification-item-container" key={n.id}>
              <div className="dot dotUnread"></div>
              <div className="notification-item read">
                <div className="notification-header">
                    
                    <span className="time">
                      {formatRelativeLabel(n.sentEpoch)}
                    </span>
                  </div>
                <div
                  className="notification-content"
                  onClick={() => handleOrderManagementNavigation(n.notification_route, location.pathname, handleRowClick)}
                >
                  
                  
                  <p className="message"><span className="title">{n.notification_title}</span> {n.message}</p>
                  <div className="clear-btn" onClick={() => handleRowClear(n.id)}>
                    <CloseIcon />
                  </div>
                </div>
              </div>
            </div>
          )})}
        </div>
      )}
    </div>
  );
}
