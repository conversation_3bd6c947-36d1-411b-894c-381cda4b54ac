// @ts-nocheck
import { useMutation } from "@tanstack/react-query";
import { reactQueryKeys } from "../common";
import axios from "axios";
import { useSubscriptionStore } from "@bryzos/giss-ui-library";

const useGetMutateSubscriptionPricing = () => {
  const { setSubscriptionsPricing } = useSubscriptionStore();

  return useMutation({
    mutationFn: async () => {
      try {
        const response = await axios.get(
          `${import.meta.env.VITE_API_SUBSCRIPTION_SERVICE}/subscription-service-api/api/v1/subscriptions/plans`
        );
        if (response.data?.data) {
          if (
            typeof response.data.data === "object" &&
            "error_message" in response.data.data
          ) {
            throw new Error(response.data.data.error_message);
          } else {
            const planBasic = response.data.data[0];
            setSubscriptionsPricing(planBasic);
          }
        } else {
          return null;
        }
      } catch (error) {
        throw new Error(error?.message ?? error);
      }
    },
  });
};

export default useGetMutateSubscriptionPricing;
