import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const usePostSaveUserListSubscription = () => {
  return useMutation(async (data: any) => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_SUBSCRIPTION_SERVICE}/subscription-service-api/api/v1/subscriptions/users/list`,
        data
      );

      if (response.data?.data) {
          return response.data.data;
      } else {
        return null;
      }
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default usePostSaveUserListSubscription;
