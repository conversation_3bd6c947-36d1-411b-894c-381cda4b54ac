import { useMutation, UseMutationOptions } from "@tanstack/react-query";
import axios from "axios";

const usePostRestoreDeletedItem = (
  options?: UseMutationOptions<any, Error, any>
) => {
  return useMutation<any, Error, any>(
    async (data: any) => {
      const response = await axios.post(
        `${import.meta.env.VITE_API_ORDER_SERVICE}/buyer/restore-deleted-item`,
        data
      );

      if (response.data?.data) {
        if (
          typeof response.data.data === "object" &&
          "error_message" in response.data.data
        ) {
          throw new Error(response.data.data.error_message);
        } else {
          return response.data.data;
        }
      } else {
        return null;
      }
    },
    options
  );
};

export default usePostRestoreDeletedItem;