import { useMutation } from "@tanstack/react-query";
import axios from "axios";
import { commomKeys } from "@bryzos/giss-ui-library";
import useDialogStore from "src/renderer2/component/DialogPopup/DialogStore";

const ENDPOINT =
  `${import.meta.env.VITE_API_NOTIFICATION_SERVICE}/notification/mark-is-read-in-app-notifications`;

export default function usePostMarkReadInAppNotifications() {
  const { showCommonDialog, resetDialogStore }: any = useDialogStore();

  return useMutation(async (ids: string[]) => {
    try {
      const res = await axios.post(
        ENDPOINT,
        { data: ids }
      );
      return !!res.data;
    } catch (error: any) {
      showCommonDialog(
        null,
        commomKeys.errorContent,
        commomKeys.actionStatus.error,
        resetDialogStore,
        [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
      );
      throw new Error(error?.message);
    }
  });
}
