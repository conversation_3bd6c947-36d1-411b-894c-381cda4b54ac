// @ts-nocheck
import { useMutation } from "@tanstack/react-query";
import { reactQueryKeys, systemVersionWebConst } from "../common";
import axios from "axios";
import { getChannelWindow } from "@bryzos/giss-ui-library";

const useSaveLoginActivityAnalytics = () => {
  return useMutation(async ({email,deviceId,ui_version,os_version,reason,last_login_app_version,event_name="COGNITO-LOGIN-FAILURE"}) => {
      const channelWindow = getChannelWindow();
      let osVersion = os_version;
      if (!osVersion) {
          if (channelWindow?.systemVersion) {
              const os = window.electron.sendSync({ channel: channelWindow.systemVersion, data: null });
              osVersion = os;
          } else {
              osVersion = systemVersionWebConst
          }
      }

      let lastLoginAppVersion = last_login_app_version ;
      if (!lastLoginAppVersion) {
          if (channelWindow?.electronVersion) {
              lastLoginAppVersion = window.electron.sendSync({ channel: channelWindow.electronVersion });
          } else {
              lastLoginAppVersion = packageConfig.version;
          }
      }

      let device_id = deviceId ;
      if (!device_id) {
          if (channelWindow?.getDeviceId) {
              device_id = window.electron.sendSync({ channel: channelWindow.getDeviceId });
          }
      }

        const payload = {
            "data":{
                email_id: email ?? "",
                device_id: device_id ?? "",
                ui_version: import.meta.env.VITE_RENDERER_DEPLOY_VERSION ?? "",
                os_version: osVersion ?? "",
                reason: reason ?? "",
                last_login_app_version: lastLoginAppVersion ?? "",
                event_name: event_name
            }
        }
        try {
            const response = await axios.post(`${import.meta.env.VITE_API_SERVICE}/user/cognito-logs`, payload);
        } catch (error:any) {
            console.log(error)
        }
});
};

export default useSaveLoginActivityAnalytics;
