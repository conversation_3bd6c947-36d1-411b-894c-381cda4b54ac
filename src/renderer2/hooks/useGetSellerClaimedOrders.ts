// @ts-nocheck
import { useQuery } from "@tanstack/react-query";
import { reactQueryKeys } from "../common";
import axios from "axios";
import { userRole, useGlobalStore } from "@bryzos/giss-ui-library";

const useGetSellerClaimedOrders = ( userData) => {
  const { setShowLoader } = useGlobalStore();
  return useQuery(
    [reactQueryKeys.getSellerClaimedOrders],
    async () => {
      try {
        const response = await axios.get(
          `${import.meta.env.VITE_API_ORDER_SERVICE}/seller/claim-orders`
        );

        if (response.data && response.data.data) {
          if (
            typeof response.data.data === "object" &&
            "error_message" in response.data.data
          ) {
            return [];
          } else {
            return response.data.data;
          }
        } else {
          return null;
        }
      } catch (error) {
        setShowLoader(false);
        throw new Error(error?.message ?? error);
      }
    },
    {
      retry: false,
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      staleTime: 0,
      enabled: !!userData?.data && userData?.data?.type === userRole.sellerUser
    }
  );
};

export default useGetSellerClaimedOrders;
