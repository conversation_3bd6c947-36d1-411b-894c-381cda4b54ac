import { NotificationItem } from "../component/Notifications/NotificationsStore";

  export type NotificationsResponse = {
    data: NotificationItem[];
  };
  
  // ---- Mock "POST" hook (replace with real call later) ----
  export async function usePostFetchNotifications(): Promise<NotificationsResponse> {
    // simulate network latency
    await new Promise((r) => setTimeout(r, 400));
    return {
      data: [
        {
          id: "8c39d2f6-8092-449a-be18-72bb0e331680",
          notification_title: "New Pricing!",
          message:
            "Search our products now to check out the freshly updated pricing!",
          notification_route: "home",
          is_read_in_app: false,
          created_date: "2025-08-14 10:00:00",
        },
        {
          id: "4f4c5736-7c66-4be0-b66f-6854ec05f5b2",
          notification_title: "New Pricing!",
          message:
            "Search our products now to check out the freshly updated pricing!",
          notification_route: "home",
          is_read_in_app: true,
          created_date: "2025-08-13 10:00:00",
        },
      ],
    };
  }