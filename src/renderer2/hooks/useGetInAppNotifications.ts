// @ts-nocheck
import { useMutation } from "@tanstack/react-query";
import axios from "axios";
import { useGlobalStore } from "@bryzos/giss-ui-library";

const ENDPOINT =
  `${import.meta.env.VITE_API_NOTIFICATION_SERVICE}/notification/in-app-notifications`;

export default function useGetInAppNotifications() {
  return useMutation(
    async () => {
      const res = await axios.get(ENDPOINT);
      return res.data; // { data: [...] }
    },
    {
      retry: false,
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      staleTime: 0,
      onError(error: any) {
        const store = useGlobalStore.getState();
        if (
          error?.code === "ERR_NETWORK_CHANGED" ||
          error?.code === "ERR_NETWORK" ||
          error?.code === "ECONNABORTED" ||
          error?.message?.includes("Network")
        ) {
          store.setApiFailureDueToNoInternet(true);
        } else {
          return error;
        }
      },
    }
  );
}
