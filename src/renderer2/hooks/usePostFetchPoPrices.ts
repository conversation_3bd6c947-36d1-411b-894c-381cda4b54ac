import { commonUiBackendDecryption, useGlobalStore } from "@bryzos/giss-ui-library";
import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const usePostFetchPoPrices = (isEncrypted: boolean) => {
    return useMutation(async (data: any) => {
        try {
            let url = `${import.meta.env.VITE_API_PRICING_SERVICE}/api/v1/pricing/calculate-from-po`;
            let response = (await axios.post(url, { data: data })).data;
            const store = useGlobalStore.getState();

            if (typeof response.data === "object" && "error_message" in response.data) {
                response = response.data;
            } else {
                if (isEncrypted) {
                    response = JSON.parse(await commonUiBackendDecryption(response.data, store.decryptionEntity.excel_decryption_key));
                } else {
                    response = response.data;
                }
            }
            return response;
        } catch (error: any) {
            throw new Error(error?.message ?? error);
        }
    },
        {
            retry: false,
            refetchOnWindowFocus: false,
            refetchOnMount: false,
            staleTime: 0,
            onError(error) {
                const store = useGlobalStore.getState();
                if (error.code === 'ERR_NETWORK_CHANGED' || error.code === "ERR_NETWORK" || error.code === 'ECONNABORTED' || error.message.includes('Network')) {
                    store.setApiFailureDueToNoInternet(true);
                } else {
                    return error
                }
            }
        }
    );
}

export default usePostFetchPoPrices;