import { useCallback} from 'react';
import usePostRestoreDeletedItem from 'src/renderer2/hooks/usePostUndeletedItem';
import { useSearchStore, useCreatePoStore } from '@bryzos/giss-ui-library';
import { useUndeleteItem, useItems } from '../store/ItemManagerStore';

export function useRestoreDeletedItem() {
  const {
    mutateAsync: restoreDeletedItem,
    isLoading,
    reset,
  } = usePostRestoreDeletedItem({
    onSuccess: (_data, variables) => {
        const target = variables?.data;
    
        if (target) {
          undeleteItem(target);
    
          // Clear stores
          setSelectedSavedSearch(null);
          setSelectedQuote(null);
          setCreatePoData(null);
        }
    
        reset();
      },
  });

  const { selectedSavedSearch, setSelectedSavedSearch } = useSearchStore();
  const selectedQuote = useCreatePoStore((state) => state.selectedQuote);
  const setCreatePoData = useCreatePoStore((state) => state.setCreatePoData);
  const setSelectedQuote = useCreatePoStore((state) => state.setSelectedQuote);

  const undeleteItem = useUndeleteItem();
  const items = useItems();
  

  // Either a saved search or a quote
  const restoredItem = selectedSavedSearch || selectedQuote;

  const handleRestoreDeletedItem = useCallback(
    async (item?: any) => {
      const target = item ?? restoredItem;
      if (!target?.id || !target?.order_type) {
        console.error("Cannot restore: Missing item ID or order type");
        return;
      }
  
      try {
        await restoreDeletedItem({
          data: {
            id: target.id,
            order_type: target.order_type,
          },
        });
      } catch (error) {
        console.error("Failed to restore item:", error);
      }
    },
    [restoredItem, restoreDeletedItem]
  );

  return {
    restoredItem,
    handleRestoreDeletedItem,
    isLoading,
  };
}
