<svg width="33" height="34" viewBox="0 0 33 34" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#gj8x7g81ya)">
        <rect x="5.37" y="10.739" width="22.036" height="22.036" rx="4" fill="url(#q0n771k8yb)"/>
    </g>
    <path d="m11.49 21.145 3.366 3.367 6.427-6.427" stroke="#fff" stroke-width="1.92"/>
    <defs>
        <linearGradient id="q0n771k8yb" x1="5.877" y1="-34.63" x2="46.884" y2="-25.458" gradientUnits="userSpaceOnUse">
            <stop stop-color="#1C40E7"/>
            <stop offset="1" stop-color="#16B9FF"/>
        </linearGradient>
        <filter id="gj8x7g81ya" x=".001" y="0" width="32.775" height="32.776" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="-5.263"/>
            <feGaussianBlur stdDeviation="2.632"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.8 0"/>
            <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_3_2827"/>
            <feBlend in="SourceGraphic" in2="effect1_dropShadow_3_2827" result="shape"/>
        </filter>
    </defs>
</svg>
