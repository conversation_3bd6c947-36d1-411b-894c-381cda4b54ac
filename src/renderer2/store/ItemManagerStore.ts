// store.ts
import { create } from "zustand";

// Define your item type properly instead of any
type Item = { id: string; [key: string]: any };

type ItemManagerStore = {
  items: Item[];
  deletedItems: Item[];
  deleteItem: (item: Item, type: string) => void;
  undeleteItem: (item: Item) => void;
  clearDeletedItems: () => void;
};

const useItemManagerStore = create<ItemManagerStore>((set) => ({
  items: [], // active items
  deletedItems: [], // deleted items

  deleteItem: (item, type) =>
    set((state) => ({
      items: state.items.filter((i) => i.id !== item.id),
      // add to deleted items only if not already there
      deletedItems: state.deletedItems.some((i) => i.id === item.id)
        ? state.deletedItems
        : [...state.deletedItems, { ...item, order_type: type }],
    })),

  undeleteItem: (item) =>
    set((state) => ({
      items: state.items.some((i) => i.id === item.id)
        ? state.items
        : [...state.items, item],
      deletedItems: state.deletedItems.filter((i) => i.id !== item.id),
    })),

  clearDeletedItems: () => set({ deletedItems: [] }),
}));

// Selectors
export const useItems = () => useItemManagerStore((state) => state.items);
export const useDeletedItems = () =>
  useItemManagerStore((state) => state.deletedItems);

// Actions
export const useDeleteItem = () =>
  useItemManagerStore((state) => state.deleteItem);
export const useUndeleteItem = () =>
  useItemManagerStore((state) => state.undeleteItem);
export const useClearDeletedItems = () =>
  useItemManagerStore((state) => state.clearDeletedItems);