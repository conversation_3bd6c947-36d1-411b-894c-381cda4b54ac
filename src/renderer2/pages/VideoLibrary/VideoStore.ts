import { SectionPillItem } from 'src/renderer2/component/SectionPills/SectionPills';
import { create } from 'zustand';


const defaultStore = {
    selectedVideo: {},
    selectedSection: '',
    panelData:{},
    thumnailClicked: false,
    videoSectionPills:[],
    shareVideoObject:null,
    transformedSelectedVideo: null,
    showPiP: false,
    seekTime: 0,
    videoSearchText: '',
};

interface VideoStore {
    panelData: any;
    thumnailClicked: boolean;
    selectedVideo: any;
    selectedSection: string;
    videoSectionPills:SectionPillItem[];
    shareVideoObject: any;
    showPiP: boolean;
    transformedSelectedVideo: any;
    seekTime: number;
    videoSearchText: string;
    setVideoSearchText: (v: string) => void;
    setSeekTime: (v: number) => void;
    setTransformedSelectedVideo: (v: any) => void;
    setShowPiP: (v: boolean) => void;
    setShareVideoObject: (v: any) => void;
    setVideoSectionPills: (v: SectionPillItem[]) => void;
    setSelectedSection: (v: string) => void;
    setSelectedVideo: (v: any) => void;
    setThumnailClicked: (v: boolean) => void;
    setPanelData: (panelData: any) => void;
}

export const useVideoStore = create<VideoStore>((set) => ({ 
    ...defaultStore,
    setVideoSearchText: (v) => set(state => ({ videoSearchText: typeof v === 'function' ? v(state.videoSearchText) : v })),
    setSeekTime: (v) => set(state => ({ seekTime: typeof v === 'function' ? v(state.seekTime) : v })),
    setTransformedSelectedVideo: (v) => set(state => ({ transformedSelectedVideo: typeof v === 'function' ? v(state.transformedSelectedVideo) : v })),
    setShowPiP: (v) => set(state => ({ showPiP: typeof v === 'function' ? v(state.showPiP) : v })),
    setShareVideoObject: (v) => set(state => ({ shareVideoObject: typeof v === 'function' ? v(state.shareVideoObject) : v })),
    setVideoSectionPills: (v) => set(state => ({ videoSectionPills: typeof v === 'function' ? v(state.videoSectionPills) : v })),
    setSelectedSection: (v) => set(state => ({ selectedSection: typeof v === 'function' ? v(state.selectedSection) : v })),
    setSelectedVideo: (v) => set(state => ({ selectedVideo: typeof v === 'function' ? v(state.selectedVideo) : v })),
    setThumnailClicked: (v) => set(state => ({ thumnailClicked: typeof v === 'function' ? v(state.thumnailClicked) : v })),
    setPanelData: (v) => set(state => ({ panelData: typeof v === 'function' ? v(state.panelData) : v })),
}));