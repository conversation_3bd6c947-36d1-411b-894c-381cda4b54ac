import { RefObject, useRef } from "react";

import styles from '../home.module.scss';
import { ProductPricingModel } from "src/renderer2/types/Search";
import clsx from "clsx";
import PricingFeedback from "./PricingFeedback";
import PricePerUnit from "./PricePerUnit";
// import AnimatedNumber from "./AnimateNumbers";

type ProductPricingState = {
    product: ProductPricingModel,
    productDescriptionAndPricingRef: RefObject<HTMLDivElement>,
    isMouseOverOnProduct: boolean,
    showInfoPopup?: boolean
};

const ProductPricing: React.FC<ProductPricingState> = ({ product, productDescriptionAndPricingRef, isMouseOverOnProduct, showInfoPopup }) => {

    return <>
        <div className={styles.priceRating} >
            <div className={styles.priceMain}>
                <PricePerUnit
                    product={product}
                />
                {!showInfoPopup && (
                    <PricingFeedback
                        product={product}
                        productDescriptionAndPricingRef={productDescriptionAndPricingRef}
                        isMouseOverOnProduct={isMouseOverOnProduct}
                    />
                )}
                
            </div>
        </div>
    </>
}

export default ProductPricing;