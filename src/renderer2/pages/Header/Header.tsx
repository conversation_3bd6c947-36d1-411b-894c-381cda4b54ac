import React, { useEffect, useRef, useState, KeyboardEvent, useMemo } from 'react'
import styles from '../Header/Header.module.scss'
import { ReactComponent as BLogo } from '../../assets/New-images/New-Bryzos-Logo.svg';
import { ReactComponent as PinIcon } from '../../assets/New-images/Pin.svg';
import { ReactComponent as PinIconHover } from '../../assets/New-images/Pin-Hover.svg';
import { ReactComponent as MenuIcon } from '../../assets/New-images/Menu.svg';
import { ReactComponent as MenuIconHover } from '../../assets/New-images/Menu-Hover.svg';
import { ReactComponent as BackArrow } from '../../assets/New-images/Create-Account/arrow-left.svg';
import { ReactComponent as NotificationIcon } from '../../assets/New-images/Header-Icons/notification-btn.svg';
import { ReactComponent as NotificationIconHover } from '../../assets/New-images/Header-Icons/notification-btn-hover.svg';
import { ReactComponent as NotificationIconActive } from '../../assets/New-images/Header-Icons/notification-btn-active.svg';
import { ReactComponent as PlayIcon } from '../../assets/New-images/Header-Icons/video-btn.svg';
import { ReactComponent as PlayIconHover } from '../../assets/New-images/Header-Icons/video-btn-hover.svg';
import { ReactComponent as PlayIconActive } from '../../assets/New-images/Header-Icons/video-btn-active.svg';
// import { ReactComponent as ChatIcon } from '../../assets/New-images/Header-Icons/chat-btn.svg';
import { ReactComponent as QuestionIcon } from '../../assets/New-images/Header-Icons/question-btn.svg';
import { ReactComponent as QuestionIconHover } from '../../assets/New-images/Header-Icons/question-btn-hover.svg';
import { ReactComponent as QuestionIconActive } from '../../assets/New-images/Header-Icons/question-btn-active.svg';
import { ReactComponent as SettingIcon } from '../../assets/New-images/Header-Icons/setting-btn.svg';
import { ReactComponent as SettingIconHover } from '../../assets/New-images/Header-Icons/setting-btn-hover.svg';
import { ReactComponent as SettingIconActive } from '../../assets/New-images/Header-Icons/setting-btn-active.svg';
// import { ReactComponent as ShareIcon } from '../../assets/New-images/Header-Icons/share-btn.svg';
import { ReactComponent as MinimizeIcon } from '../../assets/New-images/Header-Icons/minimize-btn.svg';
import { ReactComponent as MinimizeIconHover } from '../../assets/New-images/Header-Icons/minimize-btn-hover.svg';
import { ReactComponent as AppCloseIcon } from '../../assets/New-images/Header-Icons/cross-btn.svg';
import { ReactComponent as AppCloseIconHover } from '../../assets/New-images/Header-Icons/cross-btn-hover.svg';
import { ReactComponent as SearchIcon } from '../../assets/New-images/Search.svg';
import { getChannelWindow, useAuthStore, useCreatePoStore, useGlobalStore, useSubscriptionStore } from '@bryzos/giss-ui-library';
import { routes, shareEmailTypes } from '../../common';
import { useLocation, useNavigate } from 'react-router-dom';
import { useLeftPanelStore } from '../../component/LeftPanel/LeftPanelStore';
import clsx from 'clsx';
import NewBryzosLogo from "../../assets/New-images/New-Logo.png";
import { handleOrderManagementNavigation, MENU_ANIMATION_DURATION, navigatePage } from 'src/renderer2/helper';
import { useRightWindowStore } from '../RightWindow/RightWindowStore';
import ShareEmailWindow from 'src/renderer2/component/ShareEmailWindow/ShareEmailWindow';
import { useBomPdfExtractorStore } from '../buyer/BomPdfExtractor/BomPdfExtractorStore';
import SearchBox from '../GlobalSearchField/searchBox';
import { useHeaderStore } from './HeaderStore';
import { useHoverVideoStore } from 'src/renderer2/component/LeftPanel/HoverVideoStore';
import NotificationBell from 'src/renderer2/component/Notifications/NotificationsBell';
import { useInAppNotificationStore } from 'src/renderer2/component/InAppNotifications/InAppNotificationStore';
import { Badge } from '@mui/material';
import { useVideoStore } from '../VideoLibrary/VideoStore';
import { useLastNonHeaderRoute } from 'src/renderer2/hooks/useLastNonHeaderRoute';

// Add interface for AuthStore
interface AuthStore {
    initiateLogout: (arg1: boolean, arg2: boolean, arg3: boolean) => void;
    [key: string]: any;
}

const Header = ({isMacDevice, handleDoubleClick}) => {
    const { initiateLogout } = useAuthStore() as AuthStore;
    const { setOpenLeftPanel, openLeftPanel } = useLeftPanelStore();
    const location = useLocation();
    const navigate = useNavigate();
    const lockMenuTillAnimation = useRef(false);
    const { isCreatePOModule } = useCreatePoStore();
    const { setLoadComponent, loadComponent, setShareEmailWindowProps, setShareEmailType, shareEmailType, isSharedAppHistory } = useRightWindowStore();
    const { isSubscribeClickedDuringSignup, setIsSubscribeClickedDuringSignup, userData, userSubscription, appVersion, noInternetAccessibility, apiFailureDueToNoInternet, onlineStatus } = useGlobalStore();
    const {showBackToBomUploadButton, setPdfFile, setAllBoxes} = useBomPdfExtractorStore();
    const channelWindow = getChannelWindow();
    const {setShowPiP} = useVideoStore();
    const setEmitAppCloseEvent = useGlobalStore(state => state.setEmitAppCloseEvent);
    const { setSubscriptionDialogOpen } = useSubscriptionStore();
    const fatalErrorOccurred = useGlobalStore(state => state.fatalErrorOccurred);
    const {isHoverVideoEnabled, toggleHoverVideo} = useHoverVideoStore();
    const {showInviteTeam, setShowInviteTeam} = useHeaderStore()
    const {openAutoUpdatePopup} = useGlobalStore();
    const {isImpersonatedUserLoggedIn, setTriggerExitImpersonation} = useGlobalStore();
    const currentTandC = userData?.data?.current_tnc_version;
    const acceptedTandC = userData?.data?.accepted_terms_and_condition;

    const hideNavigationOnLogout = useMemo(() => {
        return location.pathname === routes.onboardingWelcome || location.pathname === routes.onboardingDetails || location.pathname === routes.onboardingTnc || location.pathname === routes.onboardingThankYou || location.pathname === routes.loginPage || location.pathname === routes.forgotPassword || location.pathname === routes.newUpdate || location.pathname === routes.TnCPage;
    }, [location.pathname]);

    const lastNonHeaderRoute = useLastNonHeaderRoute();
    

    useEffect(()=>{
        if(showInviteTeam){
            setShowInviteTeam(false);
        }
    },[location.pathname]);

    const getUnreadCount = useInAppNotificationStore(state => state.getUnreadCount);
    
    const {toggleShowPanel, showPanel} = useInAppNotificationStore();

    const handleNotificationListOpen = (e: React.MouseEvent<HTMLElement>) => {
        toggleShowPanel();
        // hide invite team if open
        if (showInviteTeam) {
            setShowInviteTeam(false);
        }
      };

    const getTitle = () => {
        switch (location.pathname) {
            case routes.createPoPage:
            case routes.orderConfirmationPage:
                return 'CREATE PO';
            case routes.bomUploadReview:
                return isCreatePOModule ? 'CREATE PO' : 'UPLOAD BOM';
            case routes.buyerSettingPage:
                return 'SETTINGS';
            case routes.homePage:
                return 'PRICE SEARCH';
            case routes.bomUpload:
                return 'CREATE PO';
            case routes.subscribe:
                return 'SUBSCRIBE';
            case routes.onboardingDetails:
            case routes.onboardingTnc:
                return 'CREATE ACCOUNT';
            case routes.onboardingThankYou:
                return 'BRYZOS';
            case routes.savedBom:
                return 'SAVED BOM';
            case routes.bomExtractor:
                return 'UPLOAD BOM';
            // seller side Header
            case routes.sellerSettingPage:
                return 'SETTINGS';
            case routes.orderPage:
                return 'AVAILABLE ORDERS';
            case routes.acceptOrderPage:
                return 'CLAIM ORDER';
            case routes.impersonateList:
                return 'IMPERSONATE USER'
            case routes.videoLibrary:
                return 'VIDEO LIBRARY'
            case routes.viewPoHistory:
                return 'PO HISTORY'
            // Add more cases as needed for other pages
            default:
                return 'DEFAULT TITLE'; // Fallback title
        }
    };

    const handleMenuClick = () => {
        if (lockMenuTillAnimation.current) return;
        setOpenLeftPanel(!openLeftPanel);
        lockMenuTillAnimation.current = true;
        setTimeout(() => {
            lockMenuTillAnimation.current = false;
        }, MENU_ANIMATION_DURATION);
    };

    // Add keydown handler for the menu button
    const handleMenuKeyDown = (e: KeyboardEvent<HTMLDivElement>) => {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleMenuClick();
        }
    };
    const handleInviteUserClick = () => {
        setShareEmailType(shareEmailTypes.inviteUser);
        setShareEmailWindowProps({ isSharePrice: false });
        setShowInviteTeam(!showInviteTeam);
        // hide notification panel if open 
            if (showPanel) {
                toggleShowPanel();
            }
        }
        //setLoadComponent(<ShareEmailWindow />)
    

    const handleInviteUserKeyDown = (e: KeyboardEvent<HTMLDivElement>) => {
        console.log('handleInviteUserKeyDown', e.key);
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleInviteUserClick();
        }
    };

    const handleSubscribeClick = () => {
        setIsSubscribeClickedDuringSignup(true);
    }
    
    const handleBackToBomUploadClick = () => {
        setAllBoxes([]);
        setPdfFile(null);
        navigatePage(location.pathname, {path: routes.bomExtractor+'?isBack=true'})
    }
    const handleDoubleClickForWindowResize = () => {
        if(!isMacDevice){
          handleDoubleClick();
        }
    }

    const closeBtnClick = () => {
    if(channelWindow?.close){
        setEmitAppCloseEvent(true);
        window.electron.send({ channel: channelWindow.close })
    }
    }

    const minimizeBtnClick = () => {
        if(channelWindow?.minimize){
            window.electron.send({ channel: channelWindow.minimize })
        }
    }

    const handleRouting = (target: string) => {
        if (lastNonHeaderRoute && (location.pathname === routes.buyerSettingPage || location.pathname === routes.sellerSettingPage || location.pathname === routes.videoLibrary)) {
            navigate(lastNonHeaderRoute)
        } else  navigatePage(location.pathname, { path: target })
    }

    const hideActions = noInternetAccessibility || apiFailureDueToNoInternet || !onlineStatus || fatalErrorOccurred || openAutoUpdatePopup;

    return (
        <div className={styles.headerStyle} onDoubleClick={handleDoubleClickForWindowResize}>

            <span className='dragArea'></span>
            <div className={styles.headerMain}>
                <div className={styles.logoSectionMain}>

                <div data-hover-video-id="bryzos-logo">
                    <div className={clsx(styles.logoSection)}>
                        <img className={styles.bryzosLogo} src={NewBryzosLogo} alt="Logo" />
                    </div>
                </div>
                {!hideActions &&
                <div className={styles.pageName}><div>BRYZOS</div> 
                  {(import.meta.env.VITE_ENVIRONMENT === 'staging' || import.meta.env.VITE_ENVIRONMENT === 'dev') ? <span className={styles.stagingEnv}>&nbsp;Staging&nbsp;v{appVersion}</span> : 
                    (import.meta.env.VITE_ENVIRONMENT === 'qa')?<span className={styles.qaEnv}>&nbsp;QA&nbsp;v{appVersion}</span>:
                    (import.meta.env.VITE_ENVIRONMENT === 'demo' && <span className={styles.demoEnv}>&nbsp;DEMO&nbsp;v{appVersion}</span>)}
                
                </div>
}
                { isImpersonatedUserLoggedIn && <span className={styles.impersonateDetail}>
                    <span>IMPERSONATED USER -  {userData?.data?.email_id} {(currentTandC !== acceptedTandC || currentTandC === null || acceptedTandC === null) && '(TnC not accepted)'}</span>
                    <button className={styles.exitBtn} onClick={()=>{setTriggerExitImpersonation(true)}}>Exit </button>
                  </span>
                 }
                </div>
                {/* <div>{!isCreatePOModule &&
                    <button className={styles.bomBackpage} onClick={handleBackToBomUploadClick}><BackArrow/>Back to Bom Upload</button>}
                </div> */}

               <div className={styles.headerRightSection}>
               {( !hideNavigationOnLogout) &&
                <>
                    {/* {(location.pathname !== routes.onboardingDetails && location.pathname !== routes.onboardingTnc && location.pathname !== routes.onboardingThankYou && userData?.data?.type !== 'SELLER') ?
                                <div className={clsx(styles.subscribeTextDiv, location.pathname === routes.homePage && styles.priceSearchSubscribeTextDiv)}>
                                    <span className={styles.subscribeText}>SUBSCRIBE</span>
                                    <span className={styles.freeTrialText1}>FREE TRIAL: 14 DAYS LEFT</span>
                                </div>
                                :
                                userData?.data?.type !== 'SELLER' && <div className={styles.subscribeTextDiv}>
                                    <span className={styles.subscribeText1}>You are starting <br /> a free 30 day trial</span>
                                </div>} */}
                    {/* <button className={styles.subscribeBtn}>Subscribe</button> */}
                </>
               } 
                  {location.pathname !== routes.onboardingDetails && location.pathname !== routes.onboardingTnc && location.pathname !== routes.onboardingThankYou &&  location.pathname !== routes.changePassword && <>
                  {
                    (!hideNavigationOnLogout && !hideActions) && <>
                        {/* <span className={styles.mainSearch}>
                        <SearchIcon/>
                        <input type="text" placeholder="Search Your Account" />
                    </span> */}
                    <SearchBox 
                        onSubscribeClick={()=>{
                            console.log("Subscribe clicked");
                            setSubscriptionDialogOpen(true)
                        }}
                        />
                    </>
                  }
                    <div className={clsx(styles.iconMain)}>
                        <div className={styles.iconDivContainer}>
                           
                            {
                                (!hideNavigationOnLogout && !hideActions) && <>
                                        {/* <div className={styles.iconDiv} onClick={handleInviteUserClick} onKeyDown={handleInviteUserKeyDown}>
                                            <ShareIcon />
                                        </div> */}
                                         <div tabIndex={11} className={clsx(styles.iconTopInviteUser, (showInviteTeam) && styles.activeIconTopInviteUser)} onClick={handleInviteUserClick} onKeyDown={handleInviteUserKeyDown}
                                            data-hover-video-id='invite-user'>
                                             INVITE TEAM
                                        </div>
                                        {/* 
                                            <div className={clsx(styles.iconDiv, (location.pathname === routes.buyerSettingPage ) && styles.activeIconDiv)}>
                                        
                                        <div className={clsx(styles.iconDiv, (showPanel) && styles.activeIconDiv)} onClick={handleNotificationListOpen}>
                                            <NotificationIcon className={styles.iconDivImg1} />
                                            <NotificationIconHover className={styles.iconDivImg2} />
                                            <NotificationIconActive className={styles.iconDivImg3} />
                                        </div> 
                                        */}
                                        <Badge
                                            color="error"
                                            badgeContent={getUnreadCount()}
                                            max={99}
                                            invisible={!getUnreadCount()}                       // hide when 0/undefined
                                            anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
                                            slotProps={{ badge: { sx: { pointerEvents: 'none', fontSize: 10, minWidth: 18, height: 18 } } }}
                                        >
                                            <div
                                                data-hover-video-id='notification'
                                                className={clsx(styles.iconDiv, showPanel && styles.activeIconDiv)}
                                                onClick={handleNotificationListOpen}
                                            >
                                                <NotificationIcon className={styles.iconDivImg1} />
                                                <NotificationIconHover className={styles.iconDivImg2} />
                                                <NotificationIconActive className={styles.iconDivImg3} />
                                            </div>
                                        </Badge>
                                        <div className={clsx(styles.iconDiv, (location.pathname === routes.videoLibrary) && styles.activeIconDiv)} onClick={() => {
                                            handleOrderManagementNavigation(routes.videoLibrary, location.pathname, handleRouting)
                                        }}>
                                            <PlayIcon className={styles.iconDivImg1} />
                                            <PlayIconHover className={styles.iconDivImg2} />
                                            <PlayIconActive className={styles.iconDivImg3} />
                                        </div>
                                        {/* <div className={styles.iconDiv}>
                                            <ChatIcon />
                                        </div>  */}
                                        <div className={clsx(styles.iconDiv, (location.pathname === routes.buyerSettingPage || location.pathname === routes.sellerSettingPage) && styles.activeIconDiv)}
                                            onClick={() => {
                                                handleOrderManagementNavigation(userData?.data?.type === 'SELLER' ? routes.sellerSettingPage : routes.buyerSettingPage, location.pathname, handleRouting)
                                            }}
                                            data-hover-video-id='settings'
                                        >
                                            <SettingIcon className={styles.iconDivImg1} />
                                            <SettingIconHover className={styles.iconDivImg2} />
                                            <SettingIconActive className={styles.iconDivImg3} />
                                        </div>
                                        <div 
                                            className={clsx(styles.iconDiv, isHoverVideoEnabled && styles.activeIconDiv)} 
                                            onClick={() => {
                                                setShowPiP(false);
                                                toggleHoverVideo();
                                            }}>
                                            <QuestionIcon className={styles.iconDivImg1} />
                                            <QuestionIconHover className={styles.iconDivImg2} />
                                            <QuestionIconActive className={styles.iconDivImg3} />
                                        </div>

                                </>
                            }
                            {!isMacDevice && 
                                <>
                                    <div className={styles.iconDiv} onClick={minimizeBtnClick}>
                                        <MinimizeIcon className={styles.iconDivImg1} />
                                        <MinimizeIconHover className={styles.iconDivImg2} />
                                    </div>
                                    <div className={styles.iconDiv} onClick={closeBtnClick}>
                                        <AppCloseIcon className={styles.iconDivImg1} />
                                        <AppCloseIconHover className={styles.iconDivImg2} />
                                    </div>
                                </>
                            }
                        </div>

                        {/* <div
                        className={styles.iconDiv}
                        tabIndex={12}
                        onClick={() => handleMenuClick()}
                        onKeyDown={handleMenuKeyDown}
                        role="button"
                        aria-label="Toggle menu"
                        data-hover-video-id='main-menu'
                    >
                        <span className={styles.iconDivImg1}><MenuIcon /></span>
                        <span className={styles.iconDivImg2}><MenuIconHover /></span>
                    </div> */}
                    </div>
                  </>
                    
                    // :
                    // <div className={clsx(styles.iconMain, styles.iconTopSubscribe)}>
                    //     <div tabIndex={11} className={clsx(styles.iconTopInviteUser, isSubscribeClickedDuringSignup && styles.activeIconTopInviteUser)} onClick={handleSubscribeClick}>
                    //         <span>SUBSCRIBE</span>
                    //     </div>
                    // </div>
                }
               </div>

               
            </div>

        </div>
    )
}
export default Header