import { isEmail } from 'src/renderer2/helper';
import * as yup from 'yup';

export const paymentSchema = yup.object().shape({
    achCheckBox: yup.boolean(),
    bankName1: yup.string().required().trim().test("isRequired", "ACH Credit is not valid", function (value) {
        const achCheckBox = this.parent.achCheckBox;
        if (achCheckBox === false) return true;
        return !!value;
    }),
    routingNo: yup.string().required("ACH Credit is not valid").test(
      "isRequired",
      "ACH Credit is not valid",
      function (value) {
        const achCheckBox = this.parent.achCheckBox;
    
        // If achCheckBox is false, skip validation
        if (achCheckBox === false) return true;
    
        // If value is masked like 'xxxxx1234', allow it
        if (value && value.includes("x")) {
          return true;
        }
    
        // Allow only 'xxxxx1234' or exactly 9 digits
        if (!/^x{5}\d{4}$|^\d{9}$/.test(value)) {
          return false;
        }
    
        // If value is exactly 9 digits, run checksum validation
        if (/^\d{9}$/.test(value)) {
          const digits = value.split('').map(Number);
          if (digits.length !== 9) return false;
    
          const sum =
            3 * (digits[0] + digits[3] + digits[6]) +
            7 * (digits[1] + digits[4] + digits[7]) +
            (digits[2] + digits[5] + digits[8]);
    
          return sum % 10 === 0;
        }
    
        // All other valid cases fall through here
        return !!value;
      }
    ),
    accountNo: yup.string().required("ACH Credit is not valid").test(
      "isRequired",
      "ACH Credit is not valid",
      function (value) {
        const achCheckBox = this.parent.achCheckBox;
    
        // Skip validation if checkbox is not checked
        if (achCheckBox === false) return true;
    
        // Must be present
        if (!value) return false;
    
        // Allow masked values like 'xxxxx1234'
        if (/^x+\d{4}$/.test(value)) {
          return true;
        }
    
        // Must be all digits if unmasked
        if (!/^\d+$/.test(value)) {
          return false;
        }
    
        // Length must be between 8 and 17 digits
        if (value.length < 8 || value.length > 17) {
          return false;
        }
    
        return true;
      }
    ),
    remittanceEmail: yup.string().trim().required('Send Remittances to is required').test('valid-emails', 'Enter valid email', value => {
        if (!value) return true;
        const emails = value.split(',');
        const isValid = emails.every(email => email.trim() && isEmail(email.trim()));
        return isValid;
      }),
});

export type paymentFormData = yup.InferType<typeof paymentSchema>;
