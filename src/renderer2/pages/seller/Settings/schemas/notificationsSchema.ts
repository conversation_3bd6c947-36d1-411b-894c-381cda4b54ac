import * as yup from 'yup';

export const buildNotificationSchema = (referenceData?: {
  [category: string]: {
    reference_column: string;
    is_email_required?: number;
    is_text_required?: number;
    is_pusher_required?: number;
  }[];
}) => {
  const shape: Record<string, yup.AnySchema> = {};

  if (referenceData) {
    Object.entries(referenceData).forEach(([category, events]) => {
      if (category === 'OTHERS') return;

      events.forEach((event) => {
        shape[event.reference_column] = yup.object().shape({
          email: yup.boolean().nullable(),
          text: yup.boolean().nullable(),
          pusher: yup.boolean().nullable(),
        });
      });
    });
  }

  return yup.object({
    data: yup.object().shape(shape),
  });
};
