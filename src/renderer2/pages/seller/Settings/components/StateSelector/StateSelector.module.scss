.container {
  display: grid;
  grid-template-columns: repeat(17, 1fr);
  gap: 2px;
  padding: 13px 30px 15px 30px;
  width: 100%;
  max-width: 66.20%;
  border-radius: 8px;
  -webkit-backdrop-filter: blur(27px);
  backdrop-filter: blur(27px);
  background-color: rgba(128, 130, 140, 0.28);
}

.stateItem {
  width: 33.2px;
  height: 24.1px;
  padding: 2px 2px;
  border-radius: 2.4px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  cursor: pointer;
  font-family: Syncopate;
  font-size: 12.1px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: 0.48px;
  text-align: left;
  color: #c3c4ca;
  transition: background-color 0.2s ease-in-out;
  border: 1px solid transparent;
  background: transparent;
  border: 0.5px solid transparent;

  &:focus {
    background-color: #3e3e44;
  }

}

.stateItem:hover {
  border: 0.5px solid #459fff;
  color: #fff;
}

.selected {
  background-color: #459fff !important;
  color: #ffffff !important;
  font-weight: bold;
}

.selected:hover {
  background-color: #459fff;
}