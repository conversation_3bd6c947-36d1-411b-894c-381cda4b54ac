import React from 'react';
import styles from './TabNavigation.module.scss';
import { sellerSettingTabs } from 'src/renderer2/common';

interface TabNavigationProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const TabNavigation: React.FC<TabNavigationProps> = ({ activeTab, setActiveTab }) => {

  return (
    <div className={styles.tabNavigation}>
    {sellerSettingTabs.map(tab => (
      <button
        key={tab.tab}
        className={`${styles.tabButton} ${activeTab === tab.tab ? styles.active : ''}`}
        onClick={() => setActiveTab(tab.tab)}
        id={tab.tab}
        data-hover-video-id={tab.hoverVideoId}
      >
        {tab.displayValue}
      </button>
    ))}
  </div>
);
};

export default TabNavigation; 