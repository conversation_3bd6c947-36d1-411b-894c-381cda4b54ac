.tncBox {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 940px;
    justify-content: center;
    margin: 0 auto;

    .title {
        margin: 48px 0px 40px 0px;
        font-family: Syncopate;
        font-size: 28px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: -1.12px;
        text-align: center;
        color: #fff;
        text-transform: uppercase;
    }

    .tnCInnerContent {
        width: 100%;
        padding: 40px 8px 2px 40px;
        border-radius: 16px;
        background-color: #191a20;
        height: 506px;
        overflow: hidden;
        position: relative;

        .tncBoxContent {
            width: 100%;
            position: relative;

            .tnCPage {
                overflow: auto;
                height: 100%;
                max-height: 430px;

                &::-webkit-scrollbar {
                    width: 8px;
                    height: 6px;
                }
            }



            .tncScrollClass {
                .TermsofUseV1 {
                    font-family: Inter;
                    font-size: 28px;
                    font-weight: bold;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.2;
                    letter-spacing: normal;
                    text-align: left;
                    color: #fff;
                    margin-bottom: 24px;
                }
            }


        }

        // Scroll to Bottom Indicator
        .scrollToBottomIndicator {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 11px 20px;
            background-color: #3b3c41;
            padding: 16px 20px;
            border-radius: 0 0 16px 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 60px;
            transition: background-color 0.2s ease;

            &:hover {
                background-color: #3a3b42;
            }

            .scrollText {
                font-family: Inter;
                font-size: 18px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.4;
                letter-spacing: normal;
                text-align: center;
                color: #c3c4ca;
            }

            .scrollArrows {
                display: flex;
                align-items: center;
            }

           
        }

        @keyframes bounce {0%,20%,50%,80%,100% {
                transform: translateY(0);
            }

            40% {
                transform: translateY(-3px);
            }

            60% {
                transform: translateY(-2px);
            }
        }
    }

    .btnFooterTnc {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
        justify-content: space-between;
        margin-top: 66px;

        .btnBack {
            button {
                font-family: Inter;
                font-size: 16px;
                font-weight: normal;
                font-style: normal;
                line-height: 1;
                letter-spacing: normal;
                text-align: left;
                color: rgba(255, 255, 255, 0.4);
                display: flex;
                align-items: center;

                svg {
                    width: 28px;
                    height: 28px;
                    flex-grow: 0;
                    margin: 0 8px 0 0;
                    padding: 7px 8px;
                    border-radius: 6px;
                    background-color: rgba(255, 255, 255, 0.04);
                }

                span {
                    padding-top: 4px;
                }
            }
        }

        .alreadyAccountLogin {
            font-family: Inter;
            font-size: 18px;
            font-weight: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: normal;
            text-align: left;
            color: rgba(255, 255, 255, 0.4);
            margin-left: auto;
            margin-left: 64px;

            span {
                color: #fff;
                cursor: pointer;
            }
        }

        .nextTncBtn {
            width: 194px;
            height: 52px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            border-radius: 12px;
            box-shadow: 0 -5.3px 5.3px 0 rgba(0, 0, 0, 0.8);
            background-image: linear-gradient(140deg, #1c40e7 -48%, #16b9ff 132%);
            font-family: Syncopate;
            font-size: 18px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: -0.72px;
            text-align: left;
            color: #fff;
            text-transform: uppercase;

            &[disabled] {
                opacity: unset;
                border-radius: 12px;
                background-color: rgba(255, 255, 255, 0.08);
                box-shadow: none;
                background-image: unset;
                color: rgba(255, 255, 255, 0.4);

            }
        }

    }
}


.ErrorDialog {
    .dialogContent {
        max-width: 300px;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 30px 34px 30px 34px;
        object-fit: contain;
        border-radius: 10px;
        -webkit-backdrop-filter: blur(24px);
        backdrop-filter: blur(24px);
        box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
        background-color: rgba(0, 0, 0, 0.72);
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.6;
        text-align: center;
        color: #fff;

        p {
            margin-bottom: 20px;
        }


        .submitBtn {
            height: 40px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 10px 24px;
            border-radius: 4px;
            border: solid 0.5px #fff;
            background-color: transparent;
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.4;
            text-align: center;
            color: #fff;
            margin-top: 20px;
            transition: all 0.1s;

            &:hover {
                background-color: #70ff00;
                border: solid 0.5px #70ff00;
                color: #000;
            }

            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;

                &:hover {
                    border: solid 0.5px #fff;
                    background-color: transparent;
                    color: #fff;
                }
            }
        }


    }

}

.checkingThisbox {
    margin-top: 40.8px;
    display: flex;
    align-items: flex-start;
    column-gap: 21.1px;
    padding-left: 40px;
    padding-right: 45px;

    .containerChk {
        display: inline-block;
        position: relative;
        cursor: pointer;
        padding-left: 12px;
        text-align: left;

        input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }

        .checkmark {
            position: absolute;
            left: -4px;
            top: -2px;
            z-index: 1;
            width: 24px;
            height: 24px;
            background: url(../../assets/New-images/Create-Account/uncheckmark.svg) no-repeat;
            background-size: contain;

        }

        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
        }

        input:checked~.checkmark {
            background: url(../../assets/New-images/Create-Account/checkmark-new.svg) no-repeat;
            background-size: contain;
            height: 34px;
            width: 34px;
            left: -10px;
            top: -12px;
        }

        input:checked~.checkmark:after {
            display: block;
        }

        input:disabled~.checkmark {
            opacity: 0.5;
            cursor: not-allowed;
        }

        input:disabled~.checkmark {
            background: url(../../assets/New-images/Create-Account/uncheckmark.svg) no-repeat;
            background-size: contain;
            opacity: 0.5;
        }

    }

    .lblChk {
        font-family: Inter;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: normal;
        text-align: left;
        color: #dbdcde;
        transition: opacity 0.2s ease;
    }

    // Disabled state styling
    .containerChk input:disabled+.checkmark+.lblChk,
    .containerChk input:disabled~.lblChk {
        opacity: 0.5;
        color: rgba(219, 220, 222, 0.5);
    }
}

.checkingThisboxShow{
    cursor: not-allowed;
    .lblChk{
            pointer-events: none;

        opacity: 0.3;
    }
}