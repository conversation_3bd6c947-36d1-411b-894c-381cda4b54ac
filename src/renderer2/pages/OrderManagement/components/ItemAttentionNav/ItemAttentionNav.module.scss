.itemAttentionNav {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .counter {
    display: flex;
    align-items: center;
    width: 165px;
    height: 36px;
    padding: 4px 8px 4px 12px;
    border-radius: 3px;
    background-color: #d9d9d9;
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: 0.56px;
    text-align: left;
    color: #000;
    column-gap: 12px;

    .total {
      font-size: 24px;
      font-weight: bold;
    }
  }

  .itemInfo {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0 16px;
    min-width: 0;

    .itemType {
      font-size: 12px;
      font-weight: 600;
      color: #856404;
      text-transform: uppercase;
      margin-bottom: 2px;
    }

    .itemDescription {
      font-size: 14px;
      color: #495057;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .navigation {
    display: flex;
    gap: 3px;

    .navButton {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 36px;
      flex-grow: 0;
      background-color: #d9d9d9;
      cursor: pointer;
      transition: all 0.2s ease;

      &.prevButton{
        border-radius: 11px 0px 0px 11px;
        
      }

      &.nextButton{
        border-radius: 0px 11px 11px 0px;
      }

      &:disabled {
        opacity: 0.1;
        cursor: not-allowed;
      }

      svg{
        path{
          fill: #191a20;
        }
      }

    }
  }
}