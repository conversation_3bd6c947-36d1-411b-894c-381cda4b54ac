.root {
  background-color: #191a20;
  padding: 16px;
  &:not(:nth-child(2)) {
    margin-top: 16px;
  }

  .title {
    font-family: Syncopate;
    font-size: 20px;
    font-weight: bold;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: 1.4px;
    text-align: left;
    color: #fff;
    text-transform: uppercase;
  }

  .description {
    font-size: 12px;
    font-weight: normal;
    line-height: 1.4;
    letter-spacing: 0.84px;
    text-align: left;
    color: #9b9eac;
    margin-top: 8px;
    margin-bottom: 18px;
  }

  .steps {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 10px;

    li {
      font-family: Inter;
      font-size: 14px;
      line-height: 1.4;
      letter-spacing: normal;
      color: #fff;
      display: flex;
      align-items: center;
      gap: 8px;

      &[role="button"] {
        cursor: pointer;
      }

      &[data-error=true] {
          color: #71737f;
      }
    }
  }

  .continueButton {
    display: flex;
    width: 100%;
    height: 50px;
    padding: 16px;
    padding: 14px 0 8px;
    border-radius: 10px;
    background-color: #222329;
    font-size: 18px;
    line-height: 1.3;
    letter-spacing: -0.72px;
    text-align: center;
    color: rgba(255, 255, 255, 0.4);
    text-transform: uppercase;
    font-family: Syncopate;
    font-weight: bold;
    text-align: center;
    justify-content: center;
    align-items: center;
    margin-top: 24px;
  }
}
