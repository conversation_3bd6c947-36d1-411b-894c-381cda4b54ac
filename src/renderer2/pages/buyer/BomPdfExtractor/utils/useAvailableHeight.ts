import { useEffect, useRef } from "react";

export function useAvailableHeight(
  targetRef: React.RefObject<HTMLElement>,
  options?: { bottomOffset?: number }  // e.g. footer height, safe-area, etc.
) {
  const stateRef = useRef({ raf: 0 });

  useEffect(() => {
    const el = targetRef.current;
    if (!el) return;

    const bottomOffset = options?.bottomOffset ?? 0;

    const update = () => {
      // top relative to viewport
      const top = el.getBoundingClientRect().top;
      // available height in px
      const h = Math.max(0, window.innerHeight - top - bottomOffset);
      el.style.height = `${h}px`;
      el.style.overflow = "auto"; // ensure scroll
    };

    const schedule = () => {
      cancelAnimationFrame(stateRef.current.raf);
      stateRef.current.raf = requestAnimationFrame(update);
    };

    // Update on resize/scroll and when element resizes/moves
    window.addEventListener("resize", schedule, { passive: true });
    window.addEventListener("scroll", schedule, { passive: true });

    // ResizeObserver: header height/content changes push element down
    const ro = new ResizeObserver(schedule);
    ro.observe(document.documentElement);
    ro.observe(document.body);
    ro.observe(el);

    // Initial
    schedule();

    return () => {
      cancelAnimationFrame(stateRef.current.raf);
      window.removeEventListener("resize", schedule);
      window.removeEventListener("scroll", schedule);
      ro.disconnect();
    };
  }, [targetRef, options]);
}
