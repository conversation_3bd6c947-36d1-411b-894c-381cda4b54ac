import React, { useState, useRef } from 'react';
import { Dialog } from '@mui/material';
import VideoPlayer from '../../../../component/videoPlayer/videoPlayer';
import { ReactComponent as CloseXIcon } from '../../../../assets/New-images/Close-x-icon.svg';
import { ReactComponent as FullscreenIcon } from '../../../../assets/images/Fullscreen.svg';
import { ReactComponent as ExitFullscreenIcon } from '../../../../assets/images/ExitFullScreen.svg';
import styles from './VideoPopup.module.scss';

interface VideoPopupProps {
  open: boolean;
  onClose: () => void;
  videoUrl: string;
  title?: string;
  description?: string;
}

const VideoPopup: React.FC<VideoPopupProps> = ({
  open,
  onClose,
  videoUrl,
  title,
  description
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const dialogRef = useRef<HTMLDivElement>(null);

  const toggleFullscreen = async () => {
    try {
      // First try to find the video element within the VideoPlayer
      const videoElement = dialogRef.current?.querySelector('video') as HTMLVideoElement;
      
      if (videoElement && !isFullscreen) {
        // Use the video element's native fullscreen if available
        if (videoElement.requestFullscreen) {
          await videoElement.requestFullscreen();
          return;
        } else if ((videoElement as any).webkitEnterFullScreen) {
          // iOS Safari specific method
          await (videoElement as any).webkitEnterFullScreen();
          return;
        }
      }
      
      // Fallback to dialog fullscreen
      const dialogPaper = dialogRef.current?.querySelector('.MuiDialog-paper') as HTMLElement;
      
      if (!dialogPaper) {
        console.error('Dialog paper element not found');
        return;
      }

      if (!isFullscreen) {
        if (dialogPaper.requestFullscreen) {
          await dialogPaper.requestFullscreen();
        } else if ((dialogPaper as any).webkitRequestFullscreen) {
          await (dialogPaper as any).webkitRequestFullscreen();
        } else if ((dialogPaper as any).msRequestFullscreen) {
          await (dialogPaper as any).msRequestFullscreen();
        }
      } else {
        if (document.exitFullscreen) {
          await document.exitFullscreen();
        } else if ((document as any).webkitExitFullscreen) {
          await (document as any).webkitExitFullscreen();
        } else if ((document as any).msExitFullscreen) {
          await (document as any).msExitFullscreen();
        }
      }
    } catch (error) {
      console.error('Error toggling fullscreen:', error);
    }
  };

  React.useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!(
        document.fullscreenElement ||
        (document as any).webkitFullscreenElement ||
        (document as any).msFullscreenElement
      );
      setIsFullscreen(isCurrentlyFullscreen);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('msfullscreenchange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('msfullscreenchange', handleFullscreenChange);
    };
  }, []);
  return (
    <Dialog
      ref={dialogRef}
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        style: {
          backgroundColor: '#1a1a1a',
          borderRadius: isFullscreen ? 0 : '12px',
          overflow: 'hidden',
          maxWidth: isFullscreen ? '100%' : '800px',
          maxHeight: isFullscreen ? '100vh' : '90vh',
          margin: isFullscreen ? 0 : undefined,
          width: isFullscreen ? '100vw' : undefined,
          height: isFullscreen ? '100vh' : undefined
        }
      }}
      slotProps={{
        backdrop: {
          style: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)'
          }
        }
      }}
    >
      <div className={styles.videoPopupContainer}>
        <div className={styles.buttonContainer}>
          <button
            className={styles.fullscreenButton}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              toggleFullscreen();
            }}
            aria-label={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
          >
            {isFullscreen ? <ExitFullscreenIcon /> : <FullscreenIcon />}
          </button>
          <button
            className={styles.closeButton}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onClose();
            }}
            aria-label="Close video"
          >
            <CloseXIcon />
          </button>
        </div>
        
        <div className={styles.videoSection}>
          <VideoPlayer
            url={videoUrl}
            width="100%"
            height={isFullscreen ? "calc(100vh - 80px)" : "400px"}
            autoPlay={true}
            playNextVideo={() => {}}
            disableNextVideoBtn={true}
            captionUrl=""
            showCloseButton={false}
            pauseImageUrl=""
          />
        </div>
        
        {(title || description) && (
          <div className={styles.infoSection}>
            {title && <h3 className={styles.title}>{title}</h3>}
            {description && <p className={styles.description}>{description}</p>}
          </div>
        )}
      </div>
    </Dialog>
  );
};

export default VideoPopup;
