* {
    box-sizing: border-box;
}

.chatMain {
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    display: block;
    padding: 10px 20px;
    text-align: left;
    font-size: 20px;
    background: transparent;
    border-top: 1px solid #eee;
}


$primary-color: #4CAF50;

.chatContainer {
    max-width: 100%;
    margin: 0px auto;
    display: flex;
    flex-direction: column;
    height: calc(100% - 44px);
}

.innerChat{
    display: flex;
    align-items: flex-end;
    height: 600px;
    width: 100%;
    overflow-y: auto;
    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }
}

.chat {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    padding-right: 12px;
    margin-top: auto;

  
}

.message {
    padding: 10px;
    margin: 5px;
    border-radius: 5px;
    max-width: 70%;
    clear: both;
}

.messageContent {
    word-wrap: break-word;
}

.inputBox {
    display: flex;
    justify-content: space-between;
    background-color: #f9f9f9;
    padding: 10px;
    position: relative;
    bottom: 0;
    border-radius: 4px;
    z-index: 999;
}

.inputBox input {
    flex-grow: 1;
    margin-right: 10px;
    padding: 4px 8px;
    &:focus{
        outline: #9da2b2;
    }
}

.inputBox button {
    padding: 8px 15px;
    background-color: $primary-color;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}
.tooltip {
    position: relative;
    display: inline-block;
  }
  
  .tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: black;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 0;
    position: absolute;
    z-index: 1;
    bottom: 100%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
  }
  
  .tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
  }

  .scrollDownImage {
    position: absolute;
    right: 30px;
    bottom: 100px;
    width: 40px;
    height: 40px;
    background-color: #f9f9f9;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
    z-index: 1;
    svg {
      cursor: pointer;
      width: 40px;
      height: 40px;
      position: relative;
      top: 5px;
    }
  }