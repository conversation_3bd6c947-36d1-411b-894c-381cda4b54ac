.tabNavigation {
  display: flex;
  column-gap: 9px;
  margin-bottom: 1px;
}

.tabButton {
  padding: 8px 12px;
  border-radius: 500px;
  background-color: rgba(255, 255, 255, 0.04);
  border: none;
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: center;
  color: #71737f;
  cursor: pointer;
  text-transform: capitalize;
  transition: color 0.2s;

  &.active {
    background-color: #c3c4ca;
    color: #0f0f14;
  }
  &:focus{
    color: #fff;
    outline: none;
    box-shadow: 0 0 0 1px rgba(255, 255, 255);
    &.active{
        color: #0f0f14;
    }
  }
}