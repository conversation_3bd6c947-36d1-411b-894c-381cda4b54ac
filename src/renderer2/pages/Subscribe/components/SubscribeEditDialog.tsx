import { useSubscriptionStore } from '@bryzos/giss-ui-library';
import React from 'react'
import SubscriptionSetup from 'src/renderer2/component/SubscriptionDialog/components/SubscriptionSetup'
import styles from '../SubscribeUserTable.module.scss'
import { ReactComponent as CloseIcon } from '../../../assets/New-images/New-Image-latest/shipment-popup-close.svg';
import clsx from 'clsx';

const SubscribeEditDialog = ({mode, closeDialog, onSuccess}: {mode: string, closeDialog: () => void, onSuccess: (content: React.ReactNode) => void}) => {
    const { userSubscription } = useSubscriptionStore();
  return (
    <div className={styles.editPaymentInfoPopup}>
      <div className={styles.titleGrid}>
        <h1>{mode === "EDIT_LICENSES" ? "ADJUST NUMBER OF LICENSES" : "EDIT PAYMENT INFO"}</h1>
        <button className={styles.closeIcon} onClick={closeDialog}>Cancel <CloseIcon /></button>
      </div>
        
        <div className={clsx(mode === "EDIT_LICENSES" && styles.editLicenseInfoContainer ,styles.licenseInfoContainer)}><span>You have</span> {userSubscription?.licenses?.current_total} paid licenses.</div>
        <SubscriptionSetup currentMode={mode} onSuccess={onSuccess}/>
    </div>
  )
}

export default SubscribeEditDialog