import { uploadFileAndGetS3Url, useGlobalStore, useSubscriptionStore } from '@bryzos/giss-ui-library';
import React, { useRef } from 'react';
import { commomKeys, prefixUrl } from 'src/renderer2/common';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import * as XLSX from 'xlsx';
import styles from '../SubscribeUserTable.module.scss'
import { ReactComponent as CloseIcon } from '../../../assets/New-images/New-Image-latest/shipment-popup-close.svg';
import clsx from 'clsx';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';

interface UserData {
  user_name: string;
  email_id: string;
  license: boolean;
  status: string;
}

const UploadUserListDialog = ({ closeDialog }: { closeDialog: () => void }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const setShowLoader = useGlobalStore((state: any) => state.setShowLoader);
  const { userList, setUploadUserList } = useSubscriptionStore();
  const {showCommonDialog, resetDialogStore} = useDialogStore();

  const validateHeaders = (headers: string[]): { isValid: boolean; userNameIndex: number; emailIdIndex: number } => {
    const normalizedHeaders = headers.map(header => header.toLowerCase().trim());

    // Find username header (supports: username, user_name, user name)
    const userNameIndex = normalizedHeaders.findIndex(header =>
      header === 'username' || header === 'user_name' || header === 'user name'
    );

    // Find email header (supports: email id, email_id, emailid, email)
    const emailIdIndex = normalizedHeaders.findIndex(header =>
      header === 'email id' || header === 'email_id' || header === 'emailid' || header === 'email'
    );

    const isValid = userNameIndex !== -1 && emailIdIndex !== -1;

    return { isValid, userNameIndex, emailIdIndex };
  };

  const parseExcelData = (workbook: XLSX.WorkBook): UserData[] => {
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];

    if (!worksheet) {
      throw new Error('No worksheet found in the Excel file');
    }

    // Convert worksheet to JSON
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as string[][];

    if (jsonData.length < 2) {
      throw new Error('Excel file must have at least a header row and one data row');
    }

    const headers = jsonData[0].map(header => String(header).trim());
    const headerValidation = validateHeaders(headers);

    if (!headerValidation.isValid) {
      throw new Error('Excel file must have headers: Username and Email Id');
    }

    const data: UserData[] = [];

    for (let i = 1; i < jsonData.length; i++) {
      const row = jsonData[i];

      // Skip empty rows
      if (!row || row.length === 0) {
        continue;
      }

      const user_name = String(row[headerValidation.userNameIndex] || '').trim();
      const email_id = String(row[headerValidation.emailIdIndex] || '').trim();

      // Skip rows with empty username or email
      if (!user_name || !email_id) {
        console.warn(`Row ${i + 1} skipped: empty username or email`);
        continue;
      }

      // Email validation - skip rows with invalid email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email_id)) {
        console.warn(`Row ${i + 1} skipped: invalid email format - ${email_id}`);
        continue;
      }

      data.push({ user_name, email_id, license: false, status: '' });
    }

    return data;
  };

  const parseCsvData = (csvText: string): UserData[] => {
    const lines = csvText.split('\n').filter(line => line.trim() !== '');

    if (lines.length < 2) {
      throw new Error('CSV file must have at least a header row and one data row');
    }

    const headers = lines[0].split(',').map(header => header.trim());
    const headerValidation = validateHeaders(headers);

    if (!headerValidation.isValid) {
      throw new Error('CSV file must have headers: Username and Email Id');
    }

    const data: UserData[] = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(value => value.trim());

      // Skip empty lines
      if (values.length === 0 || (values.length === 1 && values[0] === '')) {
        continue;
      }

      const user_name = values[headerValidation.userNameIndex] || '';
      const email_id = values[headerValidation.emailIdIndex] || '';

      // Skip rows with empty username or email
      if (!user_name || !email_id) {
        console.warn(`Row ${i + 1} skipped: empty username or email`);
        continue;
      }

      // Email validation - skip rows with invalid email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email_id)) {
        console.warn(`Row ${i + 1} skipped: invalid email format - ${email_id}`);
        continue;
      }

      data.push({ user_name, email_id, license: false, status: '' });
    }

    return data;
  };

  const isExcelFile = (file: File): boolean => {
    const excelExtensions = ['.xlsx', '.xls'];
    const excelMimeTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ];

    return excelExtensions.some(ext => file.name.toLowerCase().endsWith(ext)) ||
      excelMimeTypes.includes(file.type);
  };

  const isCsvFile = (file: File): boolean => {
    return file.type === 'text/csv' || file.name.toLowerCase().endsWith('.csv');
  };

  const validateFileType = (file: File): void => {
    if (!isExcelFile(file) && !isCsvFile(file)) {
      throw new Error('Please select a valid Excel (.xlsx, .xls) or CSV file');
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      const file = event.target.files?.[0];

      if (!file) {
        console.error('No file selected');
        return;
      }

      // Validate file type
      validateFileType(file);

      setShowLoader(true);

      if (isExcelFile(file)) {
        // Handle Excel file
        const arrayBuffer = await file.arrayBuffer();
        const workbook = XLSX.read(arrayBuffer, { type: 'array' });
        const parsedData = parseExcelData(workbook);

        let _userList = [...parsedData];
        setUploadUserList(_userList);

        console.log('Parsed Excel data:', parsedData);
        closeDialog();
      } else {
        // Handle CSV file
        const reader = new FileReader();

        reader.onload = (e) => {
          try {
            const csvText = e.target?.result as string;
            const parsedData = parseCsvData(csvText);

            // Filter out users whose emails already exist in the current user list
            const existingEmails = userList.map((user: any) => user.email_id.toLowerCase());
            const newUsers = parsedData.filter((user: UserData) =>
              !existingEmails.includes(user.email_id.toLowerCase())
            );

            let _userList = [...newUsers];
            setUploadUserList(_userList);

            console.log('Parsed CSV data:', parsedData);
            console.log('Filtered out duplicate emails. Added:', newUsers.length, 'out of', parsedData.length, 'users');
            closeDialog();
          } catch (err) {
            console.error(err instanceof Error ? err.message : 'Error parsing CSV file');
          }
        };

        reader.onerror = () => {
          console.error('Error reading file');
        };

        reader.readAsText(file);
      }
    } catch (err) {
      console.error(err instanceof Error ? err.message : 'Error processing file');
      showCommonDialog(null, err instanceof Error ? err.message : 'Error processing file', null, resetDialogStore, [{name: commomKeys.errorBtnTitle, action: resetDialogStore}])
    } finally {
      setShowLoader(false);
    }
  };

  const handleUploadCsv = () => {
    fileInputRef.current?.click();
  };

  const downloadExcelTemplate = () => {
    // Sample data for the template
    const sampleData = [
      ['Username', 'Email Id'],
    ];

    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet(sampleData);

    // Set column widths for better readability
    const columnWidths = [
      { wch: 15 }, // Username column
      { wch: 25 }  // Email Id column
    ];
    worksheet['!cols'] = columnWidths;

    // Add the worksheet to the workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'User List Template');

    // Generate the Excel file
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

    // Create download link
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'user_list_template.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className={clsx(styles.uploadUserListPopup, styles.editPaymentInfoPopup)}>
      <div className={styles.titleGrid}>
        <h1>UPLOAD USER LIST</h1>
        <button className={styles.closeIcon} onClick={closeDialog}>Cancel <CloseIcon /></button>
      </div>
      <div className={styles.uploadUserBtnContainer}>
        <button
          onClick={downloadExcelTemplate}
        >
         Download Template (CSV)
        </button>
        <button
          onClick={handleUploadCsv}

        >
          Choose File to Upload
        </button>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          accept=".xlsx,.xls,.csv,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,text/csv"
          onChange={handleFileUpload}
          style={{ display: 'none' }}
        />

      </div>

    </div>
  );
};

export default UploadUserListDialog; 