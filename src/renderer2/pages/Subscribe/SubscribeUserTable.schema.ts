import * as yup from 'yup';

export interface User {
  id: string;
  user_name: string;
  email_id: string;
  licensed: boolean;
  action: string;
  status: string;
  isExisting: boolean; // Flag to distinguish existing vs new users
  isRemoved?: boolean; // Flag to track removed users (hidden but sent to backend)
}

// Conditional validation schema for users
export const userSchema = yup.object({
  id: yup.string().when('isExisting', {
    is: true,
    // Existing users: id is required
    then: (schema) => schema.required('ID is required for existing users'),
    // New users: id is optional (used only for form handling)
    otherwise: (schema) => schema.optional()
  }),
  isExisting: yup.boolean().required(),
  user_name: yup.string().when('isExisting', {
    is: true,
    // Existing users: username is always required
    then: (schema) => schema
      .required('User name is required')
      .min(2, 'User name must be at least 2 characters')
      .max(50, 'User name must be less than 50 characters'),
    // New users: username required only if email is provided
    otherwise: (schema) => schema
      .test('conditional-required', 'User name is required when email is provided', function(value) {
        const { email_id } = this.parent;
        const hasEmail = email_id && email_id.trim();
        const hasUsername = value && value.trim();

        // Both empty is allowed, both filled is required
        if (!hasEmail && !hasUsername) return true;
        if (hasEmail && hasUsername) return value.trim().length >= 2 && value.trim().length <= 50;

        return false; // One filled, one empty is not allowed
      })
  }),
  email_id: yup.string().when('isExisting', {
    is: true,
    // Existing users: email is always required
    then: (schema) => schema
      .required('Email is required')
      .email('Please enter a valid email address'),
    // New users: email required only if username is provided
    otherwise: (schema) => schema
      .test('conditional-required', 'Email is required when username is provided', function(value) {
        const { user_name } = this.parent;
        const hasEmail = value && value.trim();
        const hasUsername = user_name && user_name.trim();

        // Both empty is allowed, both filled is required
        if (!hasEmail && !hasUsername) return true;
        if (hasEmail && hasUsername) {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          return emailRegex.test(value.trim());
        }

        return false; // One filled, one empty is not allowed
      })
  }),
  licensed: yup.boolean(),
  action: yup.string(),
  status: yup.string()
});

// Form validation schema
export const subscribeUserTableSchema = yup.object({
  users: yup.array().of(userSchema).min(1, 'At least one user is required')
});

export type SubscribeUserTableFormData = yup.InferType<typeof subscribeUserTableSchema>;