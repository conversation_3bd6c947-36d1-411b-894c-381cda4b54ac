// excel utils (updated)
import ExcelJS from 'exceljs';

type CellStyle = {
  fill?: ExcelJS.Fill;
  font?: Partial<ExcelJS.Font>;
  border?: ExcelJS.Borders | true;          // true => thin border on all sides
  alignment?: Partial<ExcelJS.Alignment>;
  numFmt?: string;
  b?: boolean;
  i?: boolean;
  u?: boolean;
  strike?: boolean;
  color?: { argb: string };
  backgroundColor?: string;                  // ARGB, e.g., 'FFFFE699'
};

type CellData = {
  cellText: string | number | Date;
  style?: CellStyle;
  colSpan?: number;                          // merge horizontally
  rowSpan?: number;                          // merge vertically
};

export type RowData = {
  row: CellData[];
  style?: CellStyle;
  height?: number;
};

export type ExcelBuildOptions = {
  sheetName?: string;
  freeze?: { rows?: number; columns?: number };
  autoFilter?: { from: string; to: string } | string;
  logo?: {
    base64: string;                          // data without prefix
    extension: 'png' | 'jpeg';
    tl?: { col: number; row: number };       // 1-based cell coords
    ext?: { width: number; height: number }; // pixels
  };
  showGridLines?: boolean;
  /** Explicit widths (Excel character units). Index 0 => column A, 1 => B, etc. */
  columnWidths?: number[];
  /**
   * Which columns to auto-size by content.
   * undefined => all columns auto-size
   * []        => none auto-size
   * ['A','AB']=> only these letters auto-size
   */
  resizeColumns?: string[];
};

const applyBorder = (excelCell: ExcelJS.Cell, borderStyle: ExcelJS.Borders | true) => {
  const defaultBorder: ExcelJS.Borders = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    right: { style: 'thin' },
    bottom: { style: 'thin' },
  };
  excelCell.border = borderStyle === true ? defaultBorder : borderStyle;
};

function applyCustomSheetData(sheet: ExcelJS.Worksheet, data: RowData[]) {
  let currentRow = 0;

  data.forEach((rowData) => {
    // Build row values with placeholders for colSpan
    const values: (string | number | Date | null)[] = [];
    rowData.row.forEach((cell) => {
      const span = Math.max(1, cell.colSpan ?? 1);
      values.push(cell.cellText);
      for (let i = 1; i < span; i++) values.push(null);
    });

    const row = sheet.addRow(values);
    currentRow = row.number;
    if (rowData.height) row.height = rowData.height;

    // Apply styles & merges
    let col = 1;
    rowData.row.forEach((cell) => {
      const excelCell = row.getCell(col);

      // Merge row-level + cell-level style (cell wins)
      const rs: CellStyle = rowData.style ?? {};
      const cs: CellStyle = { ...rs, ...(cell.style ?? {}) };

      // Font
      const colorArgb =
        typeof (cs as any).color === 'string'
          ? (cs as any).color
          : cs.color?.argb;

      const font: Partial<ExcelJS.Font> = {
        bold: cs.b ?? rs.b,
        italic: cs.i ?? rs.i,
        underline: cs.u ?? rs.u,
        strike: cs.strike ?? rs.strike,
        color: colorArgb ? { argb: colorArgb } : undefined,
        ...cs.font,
      };
      if (Object.values(font).some(v => v !== undefined)) excelCell.font = font;

      // Alignment
      if (cs.alignment) excelCell.alignment = cs.alignment;

      // Number format
      if (cs.numFmt) excelCell.numFmt = cs.numFmt;

      // Fill
      if (cs.fill) {
        excelCell.fill = cs.fill;
      } else if (cs.backgroundColor) {
        excelCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: cs.backgroundColor } };
      }

      // Border
      if (cs.border) applyBorder(excelCell, cs.border);

      // Merge cells
      const colSpan = Math.max(1, cell.colSpan ?? 1);
      const rowSpan = Math.max(1, cell.rowSpan ?? 1);
      if (colSpan > 1 || rowSpan > 1) {
        sheet.mergeCells(
          currentRow,
          col,
          currentRow + rowSpan - 1,
          col + colSpan - 1
        );
      }

      col += colSpan;
    });
  });

  // Note: autosizing moved to createExcelBlob based on options.resizeColumns
}

/** Convert 'A' -> 1, 'Z' -> 26, 'AA' -> 27, 'AB' -> 28, etc. */
function colLetterToIndex(letter: string): number {
  let n = 0;
  const s = letter.toUpperCase().replace(/[^A-Z]/g, '');
  for (let i = 0; i < s.length; i++) {
    n = n * 26 + (s.charCodeAt(i) - 64);
  }
  return n; // 1-based
}

export const createExcelBlob = async (data: RowData[], options: ExcelBuildOptions = {}) => {
  const workbook = new ExcelJS.Workbook();
  const sheet = workbook.addWorksheet(options.sheetName ?? 'Custom');

  applyCustomSheetData(sheet, data);

  // Freeze panes
  if (options.freeze) {
    sheet.views = [{
      state: 'frozen',
      xSplit: options.freeze.columns ?? 0,
      ySplit: options.freeze.rows ?? 0,
      showGridLines:options.showGridLines ?? true,
    }];
  }

  // AutoFilter
  if (options.autoFilter) {
    sheet.autoFilter = typeof options.autoFilter === 'string'
      ? options.autoFilter
      : { from: options.autoFilter.from, to: options.autoFilter.to };
  }

  // ---- Autosize by content (controlled by resizeColumns option) ----
  const maxCol = sheet.columns.length;

  // Decide which columns to auto-resize
  let columnsToResize: number[] = [];
  if (options.resizeColumns === undefined) {
    // default: autosize all columns 1..maxCol
    columnsToResize = Array.from({ length: maxCol }, (_, i) => i + 1);
  } else if (Array.isArray(options.resizeColumns) && options.resizeColumns.length > 0) {
    columnsToResize = options.resizeColumns
      .map(col => colLetterToIndex(col))
      .filter(idx => Number.isFinite(idx) && idx >= 1 && idx <= maxCol);
  } else {
    // empty array => autosize none
    columnsToResize = [];
  }

  // Perform autosize for selected columns
  for (const c of columnsToResize) {
    let maxLength = 10;
    sheet.getColumn(c).eachCell?.((cell) => {
      const v = cell.value as any;
      const len =
        v instanceof Date
          ? 12
          : (v?.toString?.().length ?? 0);
      if (len > maxLength) maxLength = len;
    });
    sheet.getColumn(c).width = maxLength + 2;
  }

  // ---- Explicit column widths override autosize where provided ----
  if (options.columnWidths?.length) {
    options.columnWidths.forEach((w, i) => {
      if (typeof w === 'number' && w > 0) {
        sheet.getColumn(i + 1).width = w;
      }
    });
  }

  // Logo
  if (options.logo && typeof options.logo.base64 === 'string' && options.logo.base64.length) {
    const imageId = workbook.addImage({
      base64: options.logo.base64,
      extension: options.logo.extension,
    });
    sheet.addImage(imageId, {
      tl: { col: (options.logo.tl?.col ?? 1) - 1, row: (options.logo.tl?.row ?? 1) - 1 },
      ext: options.logo.ext ?? { width: 180, height: 60 },
      editAs: 'oneCell',
    });
  }

  const buffer = await workbook.xlsx.writeBuffer();
  return new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });
};
