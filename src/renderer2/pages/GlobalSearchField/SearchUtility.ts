type CategoryKey = "pricing" | "quote" | "purchase" | "order";

type GlobalSearchResultItem = {
  id: string;
  order_type?: string;

  // Common fields from non-pricing buckets
  address?: string;
  city?: string;
  state_code?: string;
  zip?: string;
  buyer_internal_PO?: string;
  lines_text?: string;          // non-pricing joined details
  delivery_date?: string;
  created_date?: string;
  grand_total?: string | number;

  // Pricing-specific fields
  title?: string;               // e.g. "hss,plate,sheet"
  product_text?: string;        // '|' separated free text
  zipcode?: string;             // pricing variant of zip

  price_unit?: string;

  score?: number;
  found_text?: string;
  search_type?: string;
};

type GlobalSearchResponse = {
  success: boolean;
  search_term: string;
  results: Record<CategoryKey, GlobalSearchResultItem[]>;
  message?: string;
};

/** Case-insensitive substring search; returns the full original string on hit. */
function findMatch(value: unknown, term: string): string | null {
  if (value == null) return null;
  const str = String(value);
  if (!term) return null;
  const idx = str.toLowerCase().indexOf(term.toLowerCase());
  return idx === -1 ? null : str;
}

/** For legacy non-pricing `lines_text` like "key: value | key: value" (may use | or ||). */
function searchInLinesText(lines: string | undefined, term: string): string | null {
  if (!lines || !term) return null;
  // split on | or || (no spaces handling)
  const segments = lines.split(/\|\|?/); 
  for (const seg of segments) {
    const valuePart = seg.split(":").slice(1).join(":").trim();
    const m = findMatch(valuePart, term);
    if (m) return m;
  }
  return null;
}

/** For pricing `product_text`: simple "|" separated free-text chunks. */
function searchInProductText(text: string | undefined, term: string): string | null {
  if (!text || !term) return null;
  const segments = text.split("|").map(s => s.trim()).filter(Boolean);
  for (const seg of segments) {
    const m = findMatch(seg, term);
    if (m) return m; // return the whole segment that matched
  }
  return null;
}

/**
 * Populate `found_text` per item (if missing) by scanning relevant fields.
 * - Pricing: title → zipcode → price_unit → segments of product_text
 * - Others: address/city/state_code/zip/buyer_internal_PO/delivery_date/created_date/grand_total/price_unit → parsed lines_text
 */
export function fillFoundText(resp: GlobalSearchResponse): GlobalSearchResponse {
  if (!resp?.results) return resp;

  const term = (resp.search_term ?? "").trim();
  const CATS: CategoryKey[] = ["pricing", "quote", "purchase", "order"];

  // Non-pricing scan order
  const NON_PRICING_FIELDS: (keyof GlobalSearchResultItem)[] = [
    "address",
    "city",
    "state_code",
    "zip",
    "buyer_internal_PO",
    "delivery_date",
    "created_date",
    "grand_total",
    "price_unit",
  ];

  for (const cat of CATS) {
    const arr = resp.results[cat];
    if (!Array.isArray(arr)) continue;

    for (const item of arr) {
      item.search_type = cat;
      const already = (item.found_text ?? "").trim();
      if (already) continue;

      let matched: string | null = null;

      if (cat === "pricing") {
        // Pricing-specific order
        matched =
          findMatch(item.title, term) ??
          findMatch(item.zipcode, term) ??
          findMatch(item.price_unit, term) ??
          searchInProductText(item.product_text, term);
      } else {
        // Legacy non-pricing order
        for (const field of NON_PRICING_FIELDS) {
          matched = findMatch(item[field], term);
          if (matched) break;
        }
        if (!matched) {
          matched = searchInLinesText(item.lines_text, term);
        }
      }

      // If no match found (or empty term), fall back to the term itself
      item.found_text = matched ?? term;
    }
  }

  return resp;
}
