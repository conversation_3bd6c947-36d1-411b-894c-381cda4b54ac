import { purchaseOrder } from "src/renderer2/common";
import { GlobalSearchResultItem, GlobalSearchResponse } from "src/renderer2/hooks/usePostGlobalSearch";

export const getSellerOrderTypeFirstLetter = (order: any) => {
    if (order.claimed_by === purchaseOrder.pending && order.is_order_hidden !== true) {
        return 'P';
    } else if (order.claimed_by === purchaseOrder.readyToClaim && order.is_order_hidden !== true) {
        return 'C';
    } 
}

export const getFilteredDataByCriteria = (searchData: any[], criteria: string) => {
    if (criteria === "preview_orders") {
      const previewGroup = searchData.find(group => group.orderType === 'Preview Orders');
      return previewGroup ? previewGroup.data : [];
    } else if (criteria === "claim_orders") {
      const claimGroup = searchData.find(group => group.orderType === 'Claim Orders');
      return claimGroup ? claimGroup.data : [];
    }
    // For "all" or other criteria, return all data
    return searchData.flatMap(group => group.data);
  };

export const getSearchDataForPreviewAndClaimOrders = (array: any[]) => {
    if(!Array.isArray(array) || array.length === 0) return [];
  
    const previewOrders = array.filter((item: any) => 
      item.claimed_by === purchaseOrder.pending && item.is_order_hidden !== true
    );
  
    const claimOrders = array.filter((item: any) => 
      item.claimed_by === purchaseOrder.readyToClaim && item.is_order_hidden !== true
    );
  
    return [
      {
        orderType: 'Preview Orders',
        data: previewOrders
      },
      {
        orderType: 'Claim Orders',
        data: claimOrders
      }
    ];
  };

  const formatDateToServerFormat = (dateString: string): string => {
    if (!dateString) return '';
    
    try {
      // Handle different input formats
      let date: Date;
      
      if (dateString.includes(' ')) {
        // Format: "09/03/2025 11:26 AM" or "2025-09-03 08:21:26"
        date = new Date(dateString);
      } else {
        // Format: "2025-09-03"
        date = new Date(dateString);
      }
      
      if (isNaN(date.getTime())) {
        return dateString; // Return original if parsing fails
      }
      
      // Convert to YYYY-MM-DD format
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0'); // getMonth() returns 0-11
      const day = String(date.getDate()).padStart(2, '0');
      
      return `${year}-${month}-${day}`;
    } catch (error) {
      console.warn('Date formatting error:', error, 'for date:', dateString);
      return dateString; // Return original if formatting fails
    }
  };

// Transform local order data to match GlobalSearchResultItem format
export const transformLocalOrderToSearchItem = (order: any): GlobalSearchResultItem => {
  // Create a searchable text from various fields
  const searchableText = [
    order.buyer_po_number,
    order.city,
    order.state_id,
    order.zip,
    order.delivery_date,
    order.created_date,
    order.seller_po_price,
    order.freight_term,
    order.total_weight,
    // Add item descriptions
    ...(order.items || []).map((item: any) => item.description)
  ].filter(Boolean).join(' ');

  return {
    id: order.id,
    order_type: order.claimed_by === purchaseOrder.pending && order.is_order_hidden !== true ? "preview_order" : 'claim_order',
    address: `${order.city}, ${order.state_id} ${order.zip}`,
    city: order.city,
    state_code: order.state_id,
    zip: order.zip,
    buyer_internal_PO: order.buyer_po_number,
    lines_text: searchableText,
    delivery_date: formatDateToServerFormat(order.delivery_date),
    created_date: formatDateToServerFormat(order.created_date),
    grand_total: order.seller_po_price,
    price_unit: "FT", // Default unit
    score: 1.0, // Default score for local results
    found_text: searchableText,
  };
};

// Search within local data
export const searchLocalOrders = (localData: any[], searchTerm: string): GlobalSearchResultItem[] => {
  if (!searchTerm || !Array.isArray(localData)) return [];
  
  const term = searchTerm.toLowerCase().trim();
  if (term.length === 0) return [];

  const results: GlobalSearchResultItem[] = [];

  localData.forEach(order => {
    // Search in various fields
    const searchableFields = [
      order.buyer_po_number,
      order.city,
      order.state_id,
      order.zip,
      order.delivery_date,
      order.created_date,
      order.seller_po_price,
      order.freight_term,
      order.total_weight,
      // Search in item descriptions
      ...(order.items || []).map((item: any) => item.description)
    ];

    const hasMatch = searchableFields.some(field => 
      field && String(field).toLowerCase().includes(term)
    );

    if (hasMatch) {
      const searchItem = transformLocalOrderToSearchItem(order);
      // Set found_text to the first matching field
      const matchingField = searchableFields.find(field => 
        field && String(field).toLowerCase().includes(term)
      );
      searchItem.found_text = matchingField || searchItem.found_text;
      results.push(searchItem);
    }
  });

  return results;
};

export const mergeSearchResults = (
    serverResponse: GlobalSearchResponse | null,
    localResults: GlobalSearchResultItem[],
    searchTerm: string
  ): GlobalSearchResponse => {
    const baseResponse: GlobalSearchResponse = {
      success: true,
      results: {
        pricing: [],
        quote: [],
        purchase: [],
        order: [],
        preview_order: [],
        claim_order: []
      },
      message: "Search completed"
    };
  
    // Add server results if available
    if (serverResponse?.results) {
      // Ensure all arrays are initialized before copying
      baseResponse.results = {
        pricing: serverResponse.results.pricing || [],
        quote: serverResponse.results.quote || [],
        purchase: serverResponse.results.purchase || [],
        order: serverResponse.results.order || [],
        preview_order: serverResponse.results.preview_order || [],
        claim_order: serverResponse.results.claim_order || []
      };
    }
  
    // Add local results to appropriate categories
    localResults.forEach(item => {
      if (item.order_type === "preview_order") {
        baseResponse.results.preview_order.push({...item, search_type: "preview_order" });
      } else if (item.order_type === "claim_order") {
        baseResponse.results.claim_order.push({ ...item, search_type: "claim_order" });
      }
    });
  
    // Update message with total count
    const totalResults = Object.values(baseResponse.results).reduce((sum, arr) => sum + arr.length, 0);
    baseResponse.message = `Search completed. Found ${totalResults} results.`;
  
    return baseResponse;
  };